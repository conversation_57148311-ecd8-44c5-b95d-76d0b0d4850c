<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>现有API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            background: #f8f9fa;
        }
        .success {
            border-left: 4px solid #28a745;
            background: #d4edda;
        }
        .error {
            border-left: 4px solid #dc3545;
            background: #f8d7da;
        }
        .loading {
            border-left: 4px solid #007bff;
            background: #d1ecf1;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .api-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .latency-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .stat-item {
            background: white;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
            border: 1px solid #ddd;
        }
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 现有API测试</h1>
        <p>测试系统中已有的真实网络测试API</p>
        
        <div class="test-section">
            <h3>📋 可用的真实API</h3>
            <div class="api-info">
                <h4>中国本土API:</h4>
                <ul>
                    <li><strong>ITDOG.CN</strong> - 中国专业网络测试平台</li>
                    <li><strong>17CE.COM</strong> - 中国网络监测服务</li>
                    <li><strong>Chinaz.COM</strong> - 站长工具网络测试</li>
                </ul>
                <h4>国际API:</h4>
                <ul>
                    <li><strong>Globalping.io</strong> - 全球网络测试服务</li>
                    <li><strong>KeyCDN Tools</strong> - CDN工具网络测试</li>
                </ul>
            </div>
        </div>

        <div class="test-section">
            <h3>🇨🇳 测试中国网站 (baidu.com)</h3>
            <button onclick="testExistingAPI('baidu.com')">开始测试</button>
            <div id="baidu-results"></div>
        </div>

        <div class="test-section">
            <h3>🌍 测试国外网站 (youtube.com)</h3>
            <button onclick="testExistingAPI('youtube.com')">开始测试</button>
            <div id="youtube-results"></div>
        </div>

        <div class="test-section">
            <h3>📊 对比测试</h3>
            <button onclick="runComparisonTest()">运行对比测试</button>
            <button onclick="clearAllResults()">清除结果</button>
            <div id="comparison-results"></div>
        </div>
    </div>

    <script>
        async function testExistingAPI(target) {
            const resultsContainer = document.getElementById(`${target.split('.')[0]}-results`);
            
            resultsContainer.innerHTML = '<div class="result loading"><h4>🔄 测试中...</h4><p>正在调用现有的真实API...</p></div>';

            try {
                // 调用现有的综合ping测试
                const response = await fetch('/api/ping-comprehensive', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ target })
                });

                const data = await response.json();
                
                if (data.success && data.results.length > 0) {
                    // 按API来源分组
                    const apiGroups = {};
                    data.results.forEach(result => {
                        const apiSource = result.apiSource || result.testMethod || 'Unknown';
                        if (!apiGroups[apiSource]) {
                            apiGroups[apiSource] = [];
                        }
                        apiGroups[apiSource].push(result);
                    });

                    let html = `<div class="result success">
                        <h4>✅ 测试成功 - ${target}</h4>
                        <p><strong>总节点数:</strong> ${data.results.length}</p>
                        <p><strong>成功平台数:</strong> ${Object.keys(apiGroups).length}</p>
                    `;

                    // 显示各API的统计
                    html += '<div class="latency-stats">';
                    Object.entries(apiGroups).forEach(([apiName, results]) => {
                        const avgLatency = Math.round(results.reduce((sum, r) => sum + r.ping, 0) / results.length);
                        const minLatency = Math.min(...results.map(r => r.ping));
                        const maxLatency = Math.max(...results.map(r => r.ping));
                        
                        html += `
                            <div class="stat-item">
                                <div class="stat-value">${avgLatency}ms</div>
                                <div class="stat-label">${apiName}</div>
                                <div class="stat-label">${results.length}节点 (${minLatency}-${maxLatency}ms)</div>
                            </div>
                        `;
                    });
                    html += '</div>';

                    // 显示详细节点信息
                    html += '<details><summary>查看详细节点数据</summary>';
                    Object.entries(apiGroups).forEach(([apiName, results]) => {
                        html += `<h5>${apiName} (${results.length}个节点):</h5><ul>`;
                        results.forEach(result => {
                            html += `<li>${result.node || result.location?.city || '未知节点'}: ${result.ping}ms</li>`;
                        });
                        html += '</ul>';
                    });
                    html += '</details></div>';

                    resultsContainer.innerHTML = html;
                } else {
                    resultsContainer.innerHTML = `
                        <div class="result error">
                            <h4>❌ 测试失败 - ${target}</h4>
                            <p>错误: ${data.error || '未知错误'}</p>
                            <p>可能原因: API服务不可用或网络问题</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultsContainer.innerHTML = `
                    <div class="result error">
                        <h4>❌ 网络错误 - ${target}</h4>
                        <p>错误: ${error.message}</p>
                    </div>
                `;
            }
        }

        async function runComparisonTest() {
            const resultsContainer = document.getElementById('comparison-results');
            
            resultsContainer.innerHTML = '<div class="result loading"><h4>🔄 运行对比测试...</h4><p>正在测试中国网站和国外网站的延迟差异...</p></div>';

            try {
                // 测试中国网站
                const baiduResponse = await fetch('/api/ping-comprehensive', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ target: 'baidu.com' })
                });
                const baiduData = await baiduResponse.json();

                // 等待一下
                await new Promise(resolve => setTimeout(resolve, 2000));

                // 测试国外网站
                const youtubeResponse = await fetch('/api/ping-comprehensive', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ target: 'youtube.com' })
                });
                const youtubeData = await youtubeResponse.json();

                if (baiduData.success && youtubeData.success) {
                    const baiduAvg = Math.round(baiduData.results.reduce((sum, r) => sum + r.ping, 0) / baiduData.results.length);
                    const youtubeAvg = Math.round(youtubeData.results.reduce((sum, r) => sum + r.ping, 0) / youtubeData.results.length);
                    const ratio = (youtubeAvg / baiduAvg).toFixed(1);

                    resultsContainer.innerHTML = `
                        <div class="result success">
                            <h4>📊 延迟对比结果</h4>
                            <div class="latency-stats">
                                <div class="stat-item">
                                    <div class="stat-value">${baiduAvg}ms</div>
                                    <div class="stat-label">百度 (中国网站)</div>
                                    <div class="stat-label">${baiduData.results.length} 个节点</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">${youtubeAvg}ms</div>
                                    <div class="stat-label">YouTube (国外网站)</div>
                                    <div class="stat-label">${youtubeData.results.length} 个节点</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-value">${ratio}x</div>
                                    <div class="stat-label">延迟差异倍数</div>
                                    <div class="stat-label">${youtubeAvg > baiduAvg ? '国外更慢' : '差异不明显'}</div>
                                </div>
                            </div>
                            <p><strong>结论:</strong> ${youtubeAvg > baiduAvg * 2 ? '国外网站延迟明显更高，符合预期' : '延迟差异不够明显，可能需要使用真实API'}</p>
                        </div>
                    `;
                } else {
                    resultsContainer.innerHTML = `
                        <div class="result error">
                            <h4>❌ 对比测试失败</h4>
                            <p>百度测试: ${baiduData.success ? '成功' : '失败'}</p>
                            <p>YouTube测试: ${youtubeData.success ? '成功' : '失败'}</p>
                        </div>
                    `;
                }
            } catch (error) {
                resultsContainer.innerHTML = `
                    <div class="result error">
                        <h4>❌ 对比测试错误</h4>
                        <p>错误: ${error.message}</p>
                    </div>
                `;
            }
        }

        function clearAllResults() {
            document.getElementById('baidu-results').innerHTML = '';
            document.getElementById('youtube-results').innerHTML = '';
            document.getElementById('comparison-results').innerHTML = '';
        }

        // 页面加载时自动运行一次测试
        window.onload = function() {
            setTimeout(() => {
                testExistingAPI('baidu.com');
            }, 1000);
        };
    </script>
</body>
</html>
