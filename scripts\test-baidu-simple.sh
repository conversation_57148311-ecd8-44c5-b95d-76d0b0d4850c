#!/bin/bash

# 简单的百度测试脚本
# 使用curl批量测试10个ping平台

echo "🚀 开始批量测试百度网站"
echo "============================================================"
echo "🎯 测试目标: https://www.baidu.com/"
echo "📊 测试平台: 10 个"
echo "⏰ 开始时间: $(date)"
echo "============================================================"

# 定义平台列表
platforms=(
    "17ce:17CE.COM:🇨🇳:⭐⭐⭐⭐⭐"
    "chinaz:Chinaz站长工具:🇨🇳:⭐⭐⭐⭐☆"
    "itdog:ITDOG.CN:🇨🇳:⭐⭐⭐⭐☆"
    "boce:BOCE.COM:🇨🇳:⭐⭐⭐☆☆"
    "alibaba-boce:阿里云BOCE:🇨🇳:⭐⭐⭐☆☆"
    "globalping:Globalping.io:🌍:⭐⭐⭐⭐☆"
    "ping-pe:Ping.pe:🌍:⭐⭐⭐⭐☆"
    "cloudflare-worker:Cloudflare Workers:⚡:⭐⭐⭐⭐☆"
    "vercel-edge:Vercel Edge Functions:⚡:⭐⭐⭐⭐☆"
    "multi-platform:Multi-Platform API:🔄:⭐⭐⭐⭐⭐"
)

target="https://www.baidu.com/"
successful=0
failed=0
total=${#platforms[@]}

# 测试每个平台
for i in "${!platforms[@]}"; do
    IFS=':' read -r platform_id platform_name platform_type platform_rating <<< "${platforms[$i]}"
    
    echo ""
    echo "$platform_type 测试 $platform_name $platform_rating"
    echo "   🎯 目标: $target"
    
    # 调用API
    response=$(curl -s -X POST http://localhost:3000/api/test-ping-platforms \
        -H "Content-Type: application/json" \
        -d "{\"platform\":\"$platform_id\",\"target\":\"$target\",\"timeout\":10000}" \
        --max-time 30)
    
    # 检查响应
    if [ $? -eq 0 ] && echo "$response" | grep -q '"success":true'; then
        echo "   ✅ 测试成功"
        
        # 提取数据
        node_count=$(echo "$response" | grep -o '"nodeCount":[0-9]*' | cut -d':' -f2)
        data_quality=$(echo "$response" | grep -o '"dataQuality":[0-9]*' | cut -d':' -f2)
        accuracy=$(echo "$response" | grep -o '"accuracy":[0-9]*' | cut -d':' -f2)
        response_time=$(echo "$response" | grep -o '"responseTime":[0-9]*' | cut -d':' -f2)
        
        echo "   📊 节点数量: ${node_count:-0} 个"
        echo "   📈 数据质量: ${data_quality:-0}%"
        echo "   🎯 准确性: ${accuracy:-0}%"
        echo "   ⏱️ 响应时间: ${response_time:-0}ms"
        
        ((successful++))
    else
        echo "   ❌ 测试失败"
        if echo "$response" | grep -q '"error"'; then
            error=$(echo "$response" | grep -o '"error":"[^"]*"' | cut -d'"' -f4)
            echo "   🚫 错误信息: $error"
        fi
        ((failed++))
    fi
    
    # 显示进度
    progress=$(( (i + 1) * 100 / total ))
    echo ""
    echo "📈 进度: $((i + 1))/$total ($progress%)"
    
    # 短暂延迟
    sleep 1
done

# 生成报告
echo ""
echo "============================================================"
echo "📊 测试结果统计报告"
echo "============================================================"
echo "✅ 成功测试: $successful/$total ($((successful * 100 / total))%)"
echo "❌ 失败测试: $failed/$total ($((failed * 100 / total))%)"

# 按类型统计
china_success=0
china_total=0
global_success=0
global_total=0
edge_success=0
edge_total=0
aggregated_success=0
aggregated_total=0

for platform in "${platforms[@]}"; do
    IFS=':' read -r platform_id platform_name platform_type platform_rating <<< "$platform"
    
    case $platform_type in
        "🇨🇳")
            ((china_total++))
            ;;
        "🌍")
            ((global_total++))
            ;;
        "⚡")
            ((edge_total++))
            ;;
        "🔄")
            ((aggregated_total++))
            ;;
    esac
done

echo ""
echo "📋 按平台类型分组统计:"
echo "  🇨🇳 中国平台: 成功率待计算"
echo "  🌍 全球平台: 成功率待计算"
echo "  ⚡ 边缘计算: 成功率待计算"
echo "  🔄 聚合API: 成功率待计算"

echo ""
echo "💡 推荐使用的平台 (针对百度网站):"
echo "  基于测试结果，建议优先使用成功的中国平台"

echo ""
echo "✅ 测试完成！"
echo "⏰ 结束时间: $(date)"
echo "============================================================"
