import type { NextApiRequest, NextApiResponse } from 'next'

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    // 检查环境变量配置状态
    const configStatus = {
      // 主力平台 (完全免费)
      mainPlatforms: {
        itdog: {
          name: 'ITDOG.CN',
          status: 'available',
          description: '中国网站测试主力',
          needsConfig: false,
          priority: 1,
          category: 'china'
        },
        globalping: {
          name: 'Globalping.io',
          status: 'available',
          description: '国际网站测试主力',
          needsConfig: false,
          priority: 1,
          category: 'international'
        }
      },

      // 备用平台 (免费)
      backupPlatforms: {
        keycdn: {
          name: 'KeyCDN Tools',
          status: 'available',
          description: 'CDN性能测试',
          needsConfig: false,
          priority: 2,
          category: 'free'
        },
        ipinfo: {
          name: 'IPInfo.io',
          status: 'available',
          description: 'IP地理定位',
          needsConfig: false,
          priority: 3,
          category: 'free'
        },
        justping: {
          name: 'Just-Ping',
          status: 'available',
          description: '全球ping服务',
          needsConfig: false,
          priority: 3,
          category: 'free'
        }
      },

      // 云服务配置
      cloudServices: {
        redis: {
          name: 'Upstash Redis',
          status: !!(process.env.REDIS_URL && process.env.KV_REST_API_TOKEN) ? 'configured' : 'not_configured',
          description: '访问计数和缓存',
          configured: !!(process.env.REDIS_URL && process.env.KV_REST_API_TOKEN)
        },
        vercel: {
          name: 'Vercel Edge Functions',
          status: !!process.env.VERCEL_EDGE_REGIONS ? 'configured' : 'not_configured',
          description: '边缘计算备用',
          configured: !!process.env.VERCEL_EDGE_REGIONS,
          regions: process.env.VERCEL_EDGE_REGIONS?.split(',') || []
        },
        cloudflare: {
          name: 'Cloudflare Worker',
          status: !!process.env.CLOUDFLARE_WORKER_URL ? 'configured' : 'not_configured',
          description: '全球分布式备用',
          configured: !!process.env.CLOUDFLARE_WORKER_URL,
          url: process.env.CLOUDFLARE_WORKER_URL || null,
          regions: process.env.CLOUDFLARE_PREFERRED_REGIONS?.split(',') || []
        }
      },

      // 可选增强平台
      optionalPlatforms: {
        uptimerobot: {
          name: 'UptimeRobot',
          status: !!process.env.UPTIMEROBOT_API_KEY ? 'configured' : 'not_configured',
          description: '监控备用',
          configured: !!process.env.UPTIMEROBOT_API_KEY
        },
        freshping: {
          name: 'Freshping',
          status: !!process.env.FRESHPING_API_KEY ? 'configured' : 'not_configured',
          description: '50个免费监控点',
          configured: !!process.env.FRESHPING_API_KEY
        },
        hetrixtools: {
          name: 'HetrixTools',
          status: !!process.env.HETRIXTOOLS_API_KEY ? 'configured' : 'not_configured',
          description: '15个免费监控点',
          configured: !!process.env.HETRIXTOOLS_API_KEY
        },
        statuscake: {
          name: 'StatusCake',
          status: !!process.env.STATUSCAKE_API_KEY ? 'configured' : 'not_configured',
          description: '专业监控平台',
          configured: !!process.env.STATUSCAKE_API_KEY
        },
        site24x7: {
          name: 'Site24x7',
          status: !!process.env.SITE24X7_API_KEY ? 'configured' : 'not_configured',
          description: '企业级监控',
          configured: !!process.env.SITE24X7_API_KEY
        },
        betteruptime: {
          name: 'BetterUptime',
          status: !!process.env.BETTERUPTIME_API_KEY ? 'configured' : 'not_configured',
          description: '现代监控平台',
          configured: !!process.env.BETTERUPTIME_API_KEY
        },
        checkly: {
          name: 'Checkly',
          status: !!process.env.CHECKLY_API_KEY ? 'configured' : 'not_configured',
          description: 'API监控专家',
          configured: !!process.env.CHECKLY_API_KEY
        }
      },

      // 系统配置
      systemConfig: {
        multiCloudTesting: process.env.ENABLE_MULTI_CLOUD_TESTING === 'true',
        realApiOnly: process.env.ENABLE_REAL_API_ONLY === 'true',
        disableSimulation: process.env.DISABLE_SIMULATION === 'true',
        debugMode: process.env.DEBUG_API_CALLS === 'true',
        logLevel: process.env.LOG_LEVEL || 'info'
      },

      // 架构信息
      architecture: {
        name: 'ITDOG.CN + Globalping.io 主力架构',
        version: '2.0.0',
        mainPlatforms: ['ITDOG.CN', 'Globalping.io'],
        backupPlatforms: ['KeyCDN', 'IPInfo.io', 'Just-Ping'],
        fallbackPlatforms: ['Multi-Platform'],
        dataQuality: '100% 真实延迟数据',
        simulationData: false
      }
    };

    // 统计信息
    const stats = {
      totalPlatforms: Object.keys(configStatus.mainPlatforms).length + 
                     Object.keys(configStatus.backupPlatforms).length + 
                     Object.keys(configStatus.optionalPlatforms).length,
      availablePlatforms: Object.values(configStatus.mainPlatforms).filter(p => p.status === 'available').length +
                         Object.values(configStatus.backupPlatforms).filter(p => p.status === 'available').length,
      configuredOptional: Object.values(configStatus.optionalPlatforms).filter(p => p.status === 'configured').length,
      cloudServicesConfigured: Object.values(configStatus.cloudServices).filter(s => s.status === 'configured').length
    };

    res.status(200).json({
      success: true,
      timestamp: new Date().toISOString(),
      configStatus,
      stats,
      recommendations: {
        immediate: [
          '✅ ITDOG.CN + Globalping.io 主力架构已就绪',
          '✅ 所有免费平台无需配置即可使用',
          '✅ 100%真实延迟数据，无模拟数据'
        ],
        optional: [
          '🔧 配置Upstash Redis可启用访问计数',
          '🌐 配置Cloudflare Worker可增强全球覆盖',
          '📊 配置Freshping可获得50个额外监控点'
        ]
      }
    });

  } catch (error) {
    console.error('配置状态检查失败:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error',
      timestamp: new Date().toISOString()
    });
  }
}
