<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>4平台并发测试验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .platform {
            display: inline-block;
            margin: 5px;
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border-radius: 20px;
            font-size: 14px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-left: 4px solid #28a745;
            border-radius: 4px;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        button {
            background: #28a745;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #218838;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .loading {
            color: #007bff;
            font-weight: bold;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 4平台并发测试验证</h1>
        <p>测试新的4个平台并发架构是否正常工作</p>
        
        <div class="test-section">
            <h3>📊 测试平台</h3>
            <div class="platform">招商银行</div>
            <div class="platform">爱奇艺</div>
            <div class="platform">百度网盘</div>
            <div class="platform">阿里云盘</div>
        </div>

        <div class="test-section">
            <h3>🎯 测试目标</h3>
            <input type="text" id="testTarget" value="baidu.com" placeholder="输入测试域名" style="width: 300px; padding: 8px; margin-right: 10px;">
            <button onclick="runTest()" id="testBtn">开始测试</button>
            <button onclick="clearResults()">清除结果</button>
        </div>

        <div class="stats" id="stats" style="display: none;">
            <div class="stat-card">
                <div class="stat-number" id="totalNodes">0</div>
                <div class="stat-label">总节点数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="successPlatforms">0</div>
                <div class="stat-label">成功平台数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="avgLatency">0</div>
                <div class="stat-label">平均延迟(ms)</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="testTime">0</div>
                <div class="stat-label">测试耗时(s)</div>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 测试结果</h3>
            <div id="results"></div>
        </div>
    </div>

    <script>
        async function runTest() {
            const target = document.getElementById('testTarget').value.trim();
            if (!target) {
                alert('请输入测试目标');
                return;
            }

            const btn = document.getElementById('testBtn');
            const results = document.getElementById('results');
            
            btn.disabled = true;
            btn.textContent = '测试中...';
            results.innerHTML = '<div class="loading">🔄 正在执行4平台并发测试...</div>';

            const startTime = Date.now();

            try {
                const response = await fetch('/api/ping-comprehensive', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ target })
                });

                const data = await response.json();
                const endTime = Date.now();
                const testDuration = ((endTime - startTime) / 1000).toFixed(1);

                if (data.success) {
                    displayResults(data, testDuration);
                } else {
                    results.innerHTML = `<div class="result error">❌ 测试失败: ${data.error}</div>`;
                }
            } catch (error) {
                results.innerHTML = `<div class="result error">❌ 网络错误: ${error.message}</div>`;
            } finally {
                btn.disabled = false;
                btn.textContent = '开始测试';
            }
        }

        function displayResults(data, testDuration) {
            const results = document.getElementById('results');
            const stats = document.getElementById('stats');
            
            // 显示统计信息
            const validResults = data.results.filter(r => r.ping > 0);
            const avgLatency = validResults.length > 0 ? 
                Math.round(validResults.reduce((sum, r) => sum + r.ping, 0) / validResults.length) : 0;
            
            document.getElementById('totalNodes').textContent = data.totalNodes || 0;
            document.getElementById('successPlatforms').textContent = data.successfulPlatforms || 0;
            document.getElementById('avgLatency').textContent = avgLatency;
            document.getElementById('testTime').textContent = testDuration;
            stats.style.display = 'grid';

            // 显示详细结果
            let html = `<div class="result">✅ 测试成功完成</div>`;
            html += `<div class="result">🏗️ 架构: ${data.architecture}</div>`;
            html += `<div class="result">📊 总节点: ${data.totalNodes}, 成功平台: ${data.successfulPlatforms}</div>`;

            // 按平台分组显示
            if (data.apiBreakdown) {
                html += '<h4>📈 各平台结果:</h4>';
                Object.entries(data.apiBreakdown).forEach(([platform, nodes]) => {
                    if (nodes.length > 0) {
                        const platformAvg = Math.round(nodes.reduce((sum, n) => sum + n.ping, 0) / nodes.length);
                        html += `<div class="result">
                            <strong>${platform}</strong>: ${nodes.length} 个节点, 平均延迟 ${platformAvg}ms
                            <br><small>节点: ${nodes.map(n => `${n.node}(${n.ping}ms)`).join(', ')}</small>
                        </div>`;
                    } else {
                        html += `<div class="result error"><strong>${platform}</strong>: 无数据</div>`;
                    }
                });
            }

            results.innerHTML = html;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('stats').style.display = 'none';
        }

        // 页面加载时自动运行一次测试
        window.onload = function() {
            setTimeout(() => {
                runTest();
            }, 1000);
        };
    </script>
</body>
</html>
