import type { NextApiRequest, NextApiResponse } from 'next'
import { performMultiCloudPing } from '../../src/services/PingService'



export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  // 只支持POST请求
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const { target } = req.body

    if (!target) {
      return res.status(400).json({ error: '缺少目标URL参数' })
    }

    console.log(`🌐 全平台真实API测试请求: ${target}`);

    // 调用真实的多云ping测试函数
    const result = await performMultiCloudPing(target);

    // 按API平台分组结果
    const apiBreakdown: { [key: string]: any[] } = {};
    if (result.success && result.results) {
      result.results.forEach((node: any) => {
        const apiSource = node.apiSource || 'Unknown';
        if (!apiBreakdown[apiSource]) {
          apiBreakdown[apiSource] = [];
        }
        apiBreakdown[apiSource].push(node);
      });
    }

    // 返回结果
    res.status(200).json({
      success: result.success,
      target: result.target,
      results: result.results,
      apiBreakdown: apiBreakdown,
      timestamp: new Date().toISOString(),
      totalPlatforms: Object.keys(apiBreakdown).length,
      successfulPlatforms: Object.values(apiBreakdown).filter(results => results.length > 0).length,
      totalNodes: result.results.length,
      architecture: 'ITDOG.CN + Globalping.io 主力架构',
      realData: true
    });

  } catch (error) {
    console.error('全平台真实API测试失败:', error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Internal server error',
      timestamp: new Date().toISOString(),
      realData: true
    });
  }
}
