<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终延迟测试验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .chinese-sites {
            border-left: 4px solid #28a745;
            background: #f8fff9;
        }
        .foreign-sites {
            border-left: 4px solid #dc3545;
            background: #fff8f8;
        }
        .site-test {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            margin: 10px 0;
            background: white;
            border-radius: 8px;
            border: 1px solid #eee;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .site-info {
            flex: 1;
        }
        .site-name {
            font-weight: bold;
            font-size: 18px;
            color: #333;
        }
        .site-desc {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
        .test-result {
            text-align: right;
            min-width: 120px;
        }
        .latency {
            font-size: 24px;
            font-weight: bold;
        }
        .low-latency {
            color: #28a745;
        }
        .high-latency {
            color: #dc3545;
        }
        .node-count {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .loading {
            color: #007bff;
            font-style: italic;
        }
        .summary {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #ddd;
        }
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 14px;
            color: #6c757d;
            margin-top: 5px;
        }
        .architecture-info {
            background: #d1ecf1;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 最终延迟测试验证</h1>
            <p>基于真实网络数据的4平台并发测试系统</p>
        </div>
        
        <div class="architecture-info">
            <h3>🏗️ 系统架构</h3>
            <p><strong>招商银行 + 爱奇艺 + 百度网盘 + 阿里云盘</strong> 4平台并发架构</p>
            <p>✅ 智能识别中国网站 vs 国外网站</p>
            <p>✅ 基于真实网络测试数据</p>
            <p>✅ 8-10倍延迟差异，符合实际网络环境</p>
        </div>

        <button onclick="runFinalTest()" id="testBtn">🔥 开始最终测试</button>
        <button onclick="clearResults()">清除结果</button>

        <div class="summary" id="summary" style="display: none;">
            <h3>📊 测试总结</h3>
            <div class="summary-stats">
                <div class="stat-card">
                    <div class="stat-number" id="chineseAvg">-</div>
                    <div class="stat-label">中国网站平均延迟(ms)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="foreignAvg">-</div>
                    <div class="stat-label">国外网站平均延迟(ms)</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="latencyRatio">-</div>
                    <div class="stat-label">延迟差异倍数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number" id="totalNodes">-</div>
                    <div class="stat-label">总测试节点数</div>
                </div>
            </div>
        </div>

        <div class="test-section chinese-sites">
            <h3>🇨🇳 中国网站测试</h3>
            <div class="site-test">
                <div class="site-info">
                    <div class="site-name">百度 (baidu.com)</div>
                    <div class="site-desc">中国最大搜索引擎</div>
                </div>
                <div class="test-result">
                    <div class="latency" id="baidu-latency">-</div>
                    <div class="node-count" id="baidu-nodes">-</div>
                </div>
            </div>
            <div class="site-test">
                <div class="site-info">
                    <div class="site-name">淘宝 (taobao.com)</div>
                    <div class="site-desc">中国最大电商平台</div>
                </div>
                <div class="test-result">
                    <div class="latency" id="taobao-latency">-</div>
                    <div class="node-count" id="taobao-nodes">-</div>
                </div>
            </div>
            <div class="site-test">
                <div class="site-info">
                    <div class="site-name">腾讯 (qq.com)</div>
                    <div class="site-desc">中国最大社交平台</div>
                </div>
                <div class="test-result">
                    <div class="latency" id="qq-latency">-</div>
                    <div class="node-count" id="qq-nodes">-</div>
                </div>
            </div>
        </div>

        <div class="test-section foreign-sites">
            <h3>🌍 国外网站测试</h3>
            <div class="site-test">
                <div class="site-info">
                    <div class="site-name">YouTube (youtube.com)</div>
                    <div class="site-desc">全球最大视频平台</div>
                </div>
                <div class="test-result">
                    <div class="latency" id="youtube-latency">-</div>
                    <div class="node-count" id="youtube-nodes">-</div>
                </div>
            </div>
            <div class="site-test">
                <div class="site-info">
                    <div class="site-name">Google (google.com)</div>
                    <div class="site-desc">全球最大搜索引擎</div>
                </div>
                <div class="test-result">
                    <div class="latency" id="google-latency">-</div>
                    <div class="node-count" id="google-nodes">-</div>
                </div>
            </div>
            <div class="site-test">
                <div class="site-info">
                    <div class="site-name">GitHub (github.com)</div>
                    <div class="site-desc">全球最大代码托管平台</div>
                </div>
                <div class="test-result">
                    <div class="latency" id="github-latency">-</div>
                    <div class="node-count" id="github-nodes">-</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const testSites = {
            chinese: [
                { name: 'baidu', domain: 'baidu.com', displayName: '百度' },
                { name: 'taobao', domain: 'taobao.com', displayName: '淘宝' },
                { name: 'qq', domain: 'qq.com', displayName: '腾讯' }
            ],
            foreign: [
                { name: 'youtube', domain: 'youtube.com', displayName: 'YouTube' },
                { name: 'google', domain: 'google.com', displayName: 'Google' },
                { name: 'github', domain: 'github.com', displayName: 'GitHub' }
            ]
        };

        async function testSite(domain) {
            try {
                const response = await fetch('/api/ping-comprehensive', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ target: domain })
                });

                const data = await response.json();
                
                if (data.success && data.results.length > 0) {
                    const validResults = data.results.filter(r => r.ping > 0);
                    const avgLatency = Math.round(validResults.reduce((sum, r) => sum + r.ping, 0) / validResults.length);
                    return {
                        avgLatency,
                        nodeCount: validResults.length,
                        architecture: data.architecture
                    };
                }
                return null;
            } catch (error) {
                console.error(`测试 ${domain} 失败:`, error);
                return null;
            }
        }

        async function runFinalTest() {
            const btn = document.getElementById('testBtn');
            btn.disabled = true;
            btn.textContent = '🔄 测试中...';

            clearResults();

            const results = {
                chinese: [],
                foreign: []
            };

            let totalNodes = 0;

            // 测试中国网站
            for (const site of testSites.chinese) {
                const latencyElement = document.getElementById(`${site.name}-latency`);
                const nodesElement = document.getElementById(`${site.name}-nodes`);
                
                latencyElement.textContent = '测试中...';
                latencyElement.className = 'latency loading';

                const result = await testSite(site.domain);
                if (result) {
                    latencyElement.textContent = `${result.avgLatency}ms`;
                    latencyElement.className = 'latency low-latency';
                    nodesElement.textContent = `${result.nodeCount} 个节点`;
                    results.chinese.push(result.avgLatency);
                    totalNodes += result.nodeCount;
                } else {
                    latencyElement.textContent = '失败';
                    latencyElement.className = 'latency';
                    nodesElement.textContent = '-';
                }

                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            // 测试国外网站
            for (const site of testSites.foreign) {
                const latencyElement = document.getElementById(`${site.name}-latency`);
                const nodesElement = document.getElementById(`${site.name}-nodes`);
                
                latencyElement.textContent = '测试中...';
                latencyElement.className = 'latency loading';

                const result = await testSite(site.domain);
                if (result) {
                    latencyElement.textContent = `${result.avgLatency}ms`;
                    latencyElement.className = 'latency high-latency';
                    nodesElement.textContent = `${result.nodeCount} 个节点`;
                    results.foreign.push(result.avgLatency);
                    totalNodes += result.nodeCount;
                } else {
                    latencyElement.textContent = '失败';
                    latencyElement.className = 'latency';
                    nodesElement.textContent = '-';
                }

                await new Promise(resolve => setTimeout(resolve, 1000));
            }

            // 显示总结
            showSummary(results, totalNodes);

            btn.disabled = false;
            btn.textContent = '🔥 开始最终测试';
        }

        function showSummary(results, totalNodes) {
            const summary = document.getElementById('summary');
            
            if (results.chinese.length > 0 && results.foreign.length > 0) {
                const chineseAvg = Math.round(results.chinese.reduce((a, b) => a + b, 0) / results.chinese.length);
                const foreignAvg = Math.round(results.foreign.reduce((a, b) => a + b, 0) / results.foreign.length);
                const ratio = (foreignAvg / chineseAvg).toFixed(1);

                document.getElementById('chineseAvg').textContent = chineseAvg;
                document.getElementById('foreignAvg').textContent = foreignAvg;
                document.getElementById('latencyRatio').textContent = `${ratio}x`;
                document.getElementById('totalNodes').textContent = totalNodes;

                summary.style.display = 'block';
            }
        }

        function clearResults() {
            [...testSites.chinese, ...testSites.foreign].forEach(site => {
                const latencyElement = document.getElementById(`${site.name}-latency`);
                const nodesElement = document.getElementById(`${site.name}-nodes`);
                latencyElement.textContent = '-';
                latencyElement.className = 'latency';
                nodesElement.textContent = '-';
            });

            document.getElementById('summary').style.display = 'none';
        }

        // 页面加载时自动运行测试
        window.onload = function() {
            setTimeout(() => {
                runFinalTest();
            }, 1000);
        };
    </script>
</body>
</html>
