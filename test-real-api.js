// 测试真实API调用脚本
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function testRealAPI() {
  console.log('🚀 开始测试真实API调用...\n');

  const testUrls = [
    'https://www.baidu.com/',
    'https://www.google.com/'
  ];

  for (const url of testUrls) {
    console.log(`📊 测试网站: ${url}`);
    
    try {
      const response = await fetch('http://localhost:3001/api/ping-cloudping', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          target: url,
          maxNodes: 10,
          fastMode: false
        }),
      });

      const data = await response.json();
      
      console.log(`✅ 成功: ${data.success}`);
      console.log(`📈 节点数量: ${data.results?.length || 0}`);
      
      if (data.results && data.results.length > 0) {
        console.log('🔍 前5个节点结果:');
        data.results.slice(0, 5).forEach((result, index) => {
          const apiSource = result.apiSource || 'Unknown';
          const testMethod = result.testMethod || 'Unknown';
          const priority = result.priority ? ` (优先级:${result.priority})` : '';
          console.log(`  ${index + 1}. ${result.node} (${result.province}): ${result.ping}ms`);
          console.log(`     🔗 API平台: ${apiSource}${priority}`);
          console.log(`     📊 测试方法: ${testMethod}`);
        });

        // 统计API来源
        const apiStats = {};
        data.results.forEach(result => {
          const api = result.apiSource || result.testMethod || 'Unknown';
          apiStats[api] = (apiStats[api] || 0) + 1;
        });

        console.log('📈 API平台统计:');
        Object.entries(apiStats).forEach(([api, count]) => {
          console.log(`  - ${api}: ${count}个节点`);
        });
      }
      
      if (data.error) {
        console.log(`❌ 错误: ${data.error}`);
      }
      
    } catch (error) {
      console.log(`❌ 请求失败: ${error.message}`);
    }
    
    console.log('---\n');
  }
}

// 运行测试
testRealAPI().catch(console.error);
