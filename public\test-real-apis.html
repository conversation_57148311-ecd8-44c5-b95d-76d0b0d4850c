<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>真实API延迟测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .api-group {
            margin: 15px 0;
            padding: 15px;
            border-left: 4px solid #007bff;
            background: #f8f9fa;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: white;
            border-radius: 5px;
            border: 1px solid #eee;
        }
        .site-name {
            font-weight: bold;
            color: #333;
            flex: 1;
        }
        .api-name {
            font-size: 12px;
            color: #666;
            margin-left: 10px;
            flex: 1;
        }
        .latency {
            font-size: 16px;
            font-weight: bold;
            min-width: 80px;
            text-align: right;
        }
        .success {
            color: #28a745;
        }
        .warning {
            color: #ffc107;
        }
        .error {
            color: #dc3545;
        }
        .loading {
            color: #007bff;
            font-style: italic;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .summary {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 15px 0;
        }
        .summary-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #ddd;
        }
        .summary-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .summary-label {
            font-size: 14px;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 真实API延迟测试</h1>
        <p>测试现有的真实网络测试API，对比国内外网站的真实延迟</p>
        
        <button onclick="runRealAPITests()" id="testBtn">开始真实API测试</button>
        <button onclick="clearResults()">清除结果</button>

        <div class="summary" id="summary" style="display: none;">
            <h3>📊 真实API测试总结</h3>
            <div class="summary-grid">
                <div class="summary-card">
                    <div class="summary-number" id="successfulAPIs">0</div>
                    <div class="summary-label">成功的API</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number" id="totalNodes">0</div>
                    <div class="summary-label">总节点数</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number" id="avgLatency">0</div>
                    <div class="summary-label">平均延迟(ms)</div>
                </div>
                <div class="summary-card">
                    <div class="summary-number" id="testDuration">0</div>
                    <div class="summary-label">测试耗时(s)</div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🇨🇳 中国网站测试 (baidu.com)</h3>
            <div class="api-group">
                <h4>中国本土API</h4>
                <div class="test-item">
                    <span class="site-name">ITDOG.CN</span>
                    <span class="api-name">中国专业网络测试</span>
                    <span class="latency" id="itdog-baidu">-</span>
                </div>
                <div class="test-item">
                    <span class="site-name">17CE.COM</span>
                    <span class="api-name">中国网络监测</span>
                    <span class="latency" id="17ce-baidu">-</span>
                </div>
                <div class="test-item">
                    <span class="site-name">Chinaz.COM</span>
                    <span class="api-name">站长工具</span>
                    <span class="latency" id="chinaz-baidu">-</span>
                </div>
            </div>
            <div class="api-group">
                <h4>国际API</h4>
                <div class="test-item">
                    <span class="site-name">Globalping.io</span>
                    <span class="api-name">全球网络测试</span>
                    <span class="latency" id="globalping-baidu">-</span>
                </div>
                <div class="test-item">
                    <span class="site-name">KeyCDN Tools</span>
                    <span class="api-name">CDN工具</span>
                    <span class="latency" id="keycdn-baidu">-</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>🌍 国外网站测试 (youtube.com)</h3>
            <div class="api-group">
                <h4>中国本土API</h4>
                <div class="test-item">
                    <span class="site-name">ITDOG.CN</span>
                    <span class="api-name">中国专业网络测试</span>
                    <span class="latency" id="itdog-youtube">-</span>
                </div>
                <div class="test-item">
                    <span class="site-name">17CE.COM</span>
                    <span class="api-name">中国网络监测</span>
                    <span class="latency" id="17ce-youtube">-</span>
                </div>
                <div class="test-item">
                    <span class="site-name">Chinaz.COM</span>
                    <span class="api-name">站长工具</span>
                    <span class="latency" id="chinaz-youtube">-</span>
                </div>
            </div>
            <div class="api-group">
                <h4>国际API</h4>
                <div class="test-item">
                    <span class="site-name">Globalping.io</span>
                    <span class="api-name">全球网络测试</span>
                    <span class="latency" id="globalping-youtube">-</span>
                </div>
                <div class="test-item">
                    <span class="site-name">KeyCDN Tools</span>
                    <span class="api-name">CDN工具</span>
                    <span class="latency" id="keycdn-youtube">-</span>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h3>📋 详细测试结果</h3>
            <div id="detailResults"></div>
        </div>
    </div>

    <script>
        const testAPIs = [
            { name: 'ITDOG', endpoint: '/api/test-itdog' },
            { name: '17CE', endpoint: '/api/test-17ce' },
            { name: 'Chinaz', endpoint: '/api/test-chinaz' },
            { name: 'Globalping', endpoint: '/api/test-globalping' },
            { name: 'KeyCDN', endpoint: '/api/test-keycdn' }
        ];

        const testSites = [
            { name: 'baidu.com', type: 'chinese' },
            { name: 'youtube.com', type: 'foreign' }
        ];

        async function testSingleAPI(apiName, site) {
            try {
                const response = await fetch('/api/ping-comprehensive', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ 
                        target: site,
                        apiFilter: apiName // 只测试指定API
                    })
                });

                const data = await response.json();
                
                if (data.success && data.results.length > 0) {
                    const apiResults = data.results.filter(r => r.apiSource === apiName);
                    if (apiResults.length > 0) {
                        const avgLatency = Math.round(apiResults.reduce((sum, r) => sum + r.ping, 0) / apiResults.length);
                        return {
                            success: true,
                            avgLatency,
                            nodeCount: apiResults.length,
                            results: apiResults
                        };
                    }
                }
                return { success: false, error: 'No results' };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        async function runRealAPITests() {
            const btn = document.getElementById('testBtn');
            btn.disabled = true;
            btn.textContent = '测试中...';

            clearResults();

            const startTime = Date.now();
            let successfulAPIs = 0;
            let totalNodes = 0;
            let allLatencies = [];
            let detailResults = [];

            // 测试每个API对每个网站
            for (const api of testAPIs) {
                for (const site of testSites) {
                    const elementId = `${api.name.toLowerCase()}-${site.name.split('.')[0]}`;
                    const element = document.getElementById(elementId);
                    
                    if (element) {
                        element.textContent = '测试中...';
                        element.className = 'latency loading';

                        const result = await testSingleAPI(api.name, site.name);
                        
                        if (result.success) {
                            element.textContent = `${result.avgLatency}ms (${result.nodeCount}节点)`;
                            element.className = 'latency success';
                            successfulAPIs++;
                            totalNodes += result.nodeCount;
                            allLatencies.push(result.avgLatency);
                            
                            detailResults.push({
                                api: api.name,
                                site: site.name,
                                success: true,
                                avgLatency: result.avgLatency,
                                nodeCount: result.nodeCount,
                                results: result.results
                            });
                        } else {
                            element.textContent = '失败';
                            element.className = 'latency error';
                            
                            detailResults.push({
                                api: api.name,
                                site: site.name,
                                success: false,
                                error: result.error
                            });
                        }

                        // 等待一下再测试下一个
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                }
            }

            const endTime = Date.now();
            const duration = ((endTime - startTime) / 1000).toFixed(1);

            // 显示总结
            showSummary(successfulAPIs, totalNodes, allLatencies, duration);
            
            // 显示详细结果
            showDetailResults(detailResults);

            btn.disabled = false;
            btn.textContent = '开始真实API测试';
        }

        function showSummary(successfulAPIs, totalNodes, allLatencies, duration) {
            const summary = document.getElementById('summary');
            
            const avgLatency = allLatencies.length > 0 ? 
                Math.round(allLatencies.reduce((a, b) => a + b, 0) / allLatencies.length) : 0;

            document.getElementById('successfulAPIs').textContent = successfulAPIs;
            document.getElementById('totalNodes').textContent = totalNodes;
            document.getElementById('avgLatency').textContent = avgLatency;
            document.getElementById('testDuration').textContent = duration;

            summary.style.display = 'block';
        }

        function showDetailResults(results) {
            const container = document.getElementById('detailResults');
            let html = '';

            results.forEach(result => {
                if (result.success) {
                    html += `
                        <div class="api-group">
                            <h4>${result.api} - ${result.site}</h4>
                            <p><strong>平均延迟:</strong> ${result.avgLatency}ms</p>
                            <p><strong>节点数:</strong> ${result.nodeCount}</p>
                            <details>
                                <summary>查看详细节点数据</summary>
                                <pre>${JSON.stringify(result.results, null, 2)}</pre>
                            </details>
                        </div>
                    `;
                } else {
                    html += `
                        <div class="api-group">
                            <h4>${result.api} - ${result.site}</h4>
                            <p style="color: #dc3545;"><strong>失败:</strong> ${result.error}</p>
                        </div>
                    `;
                }
            });

            container.innerHTML = html;
        }

        function clearResults() {
            // 清除所有延迟显示
            const elements = document.querySelectorAll('.latency');
            elements.forEach(el => {
                el.textContent = '-';
                el.className = 'latency';
            });

            // 隐藏总结和详细结果
            document.getElementById('summary').style.display = 'none';
            document.getElementById('detailResults').innerHTML = '';
        }

        // 页面加载时自动运行测试
        window.onload = function() {
            setTimeout(() => {
                runRealAPITests();
            }, 1000);
        };
    </script>
</body>
</html>
