import React, { useState, useEffect } from 'react';
import Head from 'next/head';
import APITestDashboard from '../components/APITestDashboard';

const TestPage: React.FC = () => {
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    // 检查本地存储的主题设置
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
      setIsDarkMode(true);
    } else if (savedTheme === 'light') {
      setIsDarkMode(false);
    } else {
      // 检查系统主题偏好
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      setIsDarkMode(prefersDark);
    }
  }, []);

  const toggleTheme = () => {
    const newTheme = !isDarkMode;
    setIsDarkMode(newTheme);
    localStorage.setItem('theme', newTheme ? 'dark' : 'light');
  };

  return (
    <>
      <Head>
        <title>API测试控制台 - Ping工具</title>
        <meta name="description" content="批量测试各种ping平台API，评估性能和准确性" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <div className={isDarkMode ? 'dark' : ''}>
        {/* 主题切换按钮 */}
        <div className="fixed top-4 right-4 z-50">
          <button
            onClick={toggleTheme}
            className={`p-3 rounded-full shadow-lg transition-colors duration-300 ${
              isDarkMode 
                ? 'bg-gray-800 text-yellow-400 hover:bg-gray-700' 
                : 'bg-white text-gray-600 hover:bg-gray-100'
            }`}
            title={isDarkMode ? '切换到浅色模式' : '切换到深色模式'}
          >
            {isDarkMode ? '🌞' : '🌙'}
          </button>
        </div>

        {/* 返回主页按钮 */}
        <div className="fixed top-4 left-4 z-50">
          <a
            href="/"
            className={`inline-flex items-center px-4 py-2 rounded-lg shadow-lg transition-colors duration-300 ${
              isDarkMode 
                ? 'bg-gray-800 text-white hover:bg-gray-700' 
                : 'bg-white text-gray-600 hover:bg-gray-100'
            }`}
          >
            ← 返回主页
          </a>
        </div>

        <APITestDashboard isDarkMode={isDarkMode} />
      </div>
    </>
  );
};

export default TestPage;
