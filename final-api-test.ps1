# Final Comprehensive API Test with HTML Output
Write-Host "=== Final Comprehensive API Providers Test ===" -ForegroundColor Green
Write-Host "Test Time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
Write-Host ""

$results = @()
$testStartTime = Get-Date

# Websites to test
$websites = @(
    @{ URL = "https://www.baidu.com/"; Name = "Baidu"; Category = "Domestic"; Type = "Search" },
    @{ URL = "https://www.google.com/"; Name = "Google"; Category = "Blocked"; Type = "Search" },
    @{ URL = "https://www.qq.com/"; Name = "QQ"; Category = "Domestic"; Type = "Portal" },
    @{ URL = "https://www.bilibili.com/"; Name = "Bilibili"; Category = "Domestic"; Type = "Video" },
    @{ URL = "https://www.zhihu.com/"; Name = "Zhihu"; Category = "Domestic"; Type = "Community" },
    @{ URL = "https://www.youtube.com/"; Name = "YouTube"; Category = "Blocked"; Type = "Video" },
    @{ URL = "https://www.facebook.com/"; Name = "Facebook"; Category = "Blocked"; Type = "Social" },
    @{ URL = "https://github.com/"; Name = "GitHub"; Category = "Accessible"; Type = "Developer" }
)

function Test-ITDOG {
    param($website)
    Write-Host "    ITDOG testing $($website.Name)..." -NoNewline
    try {
        $body = @{ target = $website.URL } | ConvertTo-Json
        $response = Invoke-WebRequest -Uri "http://localhost:3000/api/itdog-proxy" -Method POST -Headers @{"Content-Type"="application/json"} -Body $body -TimeoutSec 15
        $data = $response.Content | ConvertFrom-Json
        if ($data.results -and $data.results.Count -gt 0) {
            $avgLatency = ($data.results | Measure-Object ping -Average).Average
            $minLatency = ($data.results | Measure-Object ping -Minimum).Minimum
            $maxLatency = ($data.results | Measure-Object ping -Maximum).Maximum
            Write-Host " OK - Avg: $([math]::Round($avgLatency, 1))ms (Nodes: $($data.results.Count))" -ForegroundColor Green
            return @{
                Success = $true
                AvgLatency = [math]::Round($avgLatency, 1)
                MinLatency = [math]::Round($minLatency, 1)
                MaxLatency = [math]::Round($maxLatency, 1)
                NodeCount = $data.results.Count
            }
        } else {
            Write-Host " NO DATA" -ForegroundColor Red
            return @{ Success = $false; Error = "No data" }
        }
    } catch {
        Write-Host " ERROR" -ForegroundColor Red
        return @{ Success = $false; Error = "API Error" }
    }
}

function Test-Globalping {
    param($website)
    Write-Host "    Globalping testing $($website.Name)..." -NoNewline
    try {
        $body = @{ target = $website.URL } | ConvertTo-Json
        $response = Invoke-WebRequest -Uri "http://localhost:3000/api/ping-cloudping" -Method POST -Headers @{"Content-Type"="application/json"} -Body $body -TimeoutSec 20
        $data = $response.Content | ConvertFrom-Json
        if ($data.results -and $data.results.Count -gt 0) {
            $globalpingResults = $data.results | Where-Object { $_.apiSource -eq "Globalping.io" }
            if ($globalpingResults.Count -gt 0) {
                $avgLatency = ($globalpingResults | Measure-Object ping -Average).Average
                $minLatency = ($globalpingResults | Measure-Object ping -Minimum).Minimum
                $maxLatency = ($globalpingResults | Measure-Object ping -Maximum).Maximum
                Write-Host " OK - Avg: $([math]::Round($avgLatency, 1))ms (Nodes: $($globalpingResults.Count))" -ForegroundColor Green
                return @{
                    Success = $true
                    AvgLatency = [math]::Round($avgLatency, 1)
                    MinLatency = [math]::Round($minLatency, 1)
                    MaxLatency = [math]::Round($maxLatency, 1)
                    NodeCount = $globalpingResults.Count
                }
            } else {
                Write-Host " NO GLOBALPING DATA" -ForegroundColor Red
                return @{ Success = $false; Error = "No Globalping nodes" }
            }
        } else {
            Write-Host " NO DATA" -ForegroundColor Red
            return @{ Success = $false; Error = "No data" }
        }
    } catch {
        Write-Host " ERROR" -ForegroundColor Red
        return @{ Success = $false; Error = "API Error" }
    }
}

function Test-KeyCDN {
    param($website)
    Write-Host "    KeyCDN testing $($website.Name)..." -NoNewline
    try {
        $body = @{ target = $website.URL } | ConvertTo-Json
        $response = Invoke-WebRequest -Uri "http://localhost:3000/api/ping-cloudping" -Method POST -Headers @{"Content-Type"="application/json"} -Body $body -TimeoutSec 20
        $data = $response.Content | ConvertFrom-Json
        if ($data.results -and $data.results.Count -gt 0) {
            $keycdnResults = $data.results | Where-Object { $_.apiSource -eq "KeyCDN" }
            if ($keycdnResults.Count -gt 0) {
                $avgLatency = ($keycdnResults | Measure-Object ping -Average).Average
                $minLatency = ($keycdnResults | Measure-Object ping -Minimum).Minimum
                $maxLatency = ($keycdnResults | Measure-Object ping -Maximum).Maximum
                Write-Host " OK - Avg: $([math]::Round($avgLatency, 1))ms (Nodes: $($keycdnResults.Count))" -ForegroundColor Green
                return @{
                    Success = $true
                    AvgLatency = [math]::Round($avgLatency, 1)
                    MinLatency = [math]::Round($minLatency, 1)
                    MaxLatency = [math]::Round($maxLatency, 1)
                    NodeCount = $keycdnResults.Count
                }
            } else {
                Write-Host " NO KEYCDN DATA" -ForegroundColor Red
                return @{ Success = $false; Error = "No KeyCDN nodes" }
            }
        } else {
            Write-Host " NO DATA" -ForegroundColor Red
            return @{ Success = $false; Error = "No data" }
        }
    } catch {
        Write-Host " ERROR" -ForegroundColor Red
        return @{ Success = $false; Error = "API Error" }
    }
}

function Test-LocalPing {
    param($website)
    Write-Host "    Local Ping testing $($website.Name)..." -NoNewline
    try {
        $cleanUrl = $website.URL -replace "https://", "" -replace "http://", "" -replace "/$", ""
        $pingResult = Test-Connection -ComputerName $cleanUrl -Count 3 -Quiet
        if ($pingResult) {
            $result = Test-Connection -ComputerName $cleanUrl -Count 3
            $avgLatency = ($result | Measure-Object ResponseTime -Average).Average
            $minLatency = ($result | Measure-Object ResponseTime -Minimum).Minimum
            $maxLatency = ($result | Measure-Object ResponseTime -Maximum).Maximum
            Write-Host " OK - Avg: $([math]::Round($avgLatency, 1))ms" -ForegroundColor Green
            return @{
                Success = $true
                AvgLatency = [math]::Round($avgLatency, 1)
                MinLatency = [math]::Round($minLatency, 1)
                MaxLatency = [math]::Round($maxLatency, 1)
                NodeCount = 1
            }
        } else {
            Write-Host " BLOCKED" -ForegroundColor Red
            return @{ Success = $false; Error = "Blocked" }
        }
    } catch {
        Write-Host " ERROR" -ForegroundColor Red
        return @{ Success = $false; Error = "Ping Error" }
    }
}

# Execute tests
Write-Host "Starting comprehensive tests..." -ForegroundColor Blue
Write-Host ""

foreach ($website in $websites) {
    Write-Host "Testing: $($website.Name) ($($website.URL))" -ForegroundColor Cyan
    
    # Test with all providers
    $providers = @("ITDOG", "Globalping", "KeyCDN", "LocalPing")
    
    foreach ($provider in $providers) {
        $testResult = switch ($provider) {
            "ITDOG" { Test-ITDOG $website }
            "Globalping" { Test-Globalping $website }
            "KeyCDN" { Test-KeyCDN $website }
            "LocalPing" { Test-LocalPing $website }
        }
        
        $results += [PSCustomObject]@{
            WebsiteName = $website.Name
            WebsiteURL = $website.URL
            Category = $website.Category
            Type = $website.Type
            Provider = $provider
            Success = $testResult.Success
            AvgLatency = if ($testResult.Success) { $testResult.AvgLatency } else { "N/A" }
            MinLatency = if ($testResult.Success) { $testResult.MinLatency } else { "N/A" }
            MaxLatency = if ($testResult.Success) { $testResult.MaxLatency } else { "N/A" }
            NodeCount = if ($testResult.Success) { $testResult.NodeCount } else { "N/A" }
            TestTime = Get-Date -Format "HH:mm:ss"
        }
        
        Start-Sleep -Milliseconds 300
    }
    Write-Host ""
}

$testEndTime = Get-Date
$testDuration = $testEndTime - $testStartTime

# Display summary
Write-Host "Test Summary:" -ForegroundColor Green
$results | Format-Table WebsiteName, Provider, Success, AvgLatency, NodeCount -AutoSize

# Generate HTML
Write-Host "Generating HTML report..." -ForegroundColor Yellow

$htmlContent = @"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Providers Website Latency Test Report</title>
    <style>
        body { font-family: 'Segoe UI', Arial, sans-serif; margin: 0; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .container { max-width: 1400px; margin: 0 auto; background: white; border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.3); overflow: hidden; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center; }
        .header h1 { margin: 0; font-size: 2.5em; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .content { padding: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .stat-card { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 20px; border-radius: 10px; text-align: center; }
        .stat-number { font-size: 2.5em; font-weight: bold; margin-bottom: 5px; }
        table { width: 100%; border-collapse: collapse; margin-top: 20px; font-size: 14px; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 15px rgba(0,0,0,0.1); }
        th { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px 10px; text-align: center; font-weight: bold; }
        td { padding: 12px 10px; text-align: center; border-bottom: 1px solid #eee; }
        tr:hover { background-color: #f8f9fa; }
        .domestic { background-color: #e8f5e8; }
        .accessible { background-color: #fff3cd; }
        .blocked { background-color: #f8d7da; }
        .success { color: #28a745; font-weight: bold; }
        .failed { color: #dc3545; font-weight: bold; }
        .excellent { color: #28a745; font-weight: bold; }
        .good { color: #ffc107; font-weight: bold; }
        .poor { color: #dc3545; font-weight: bold; }
        .analysis { background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%); padding: 20px; border-radius: 10px; margin: 20px 0; }
        .footer { background: #f8f9fa; padding: 20px; text-align: center; color: #6c757d; border-top: 1px solid #dee2e6; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌐 API Providers Website Latency Test Report</h1>
            <p>Test Time: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') | Environment: Windows 10 + VPN</p>
        </div>

        <div class="content">
            <div class="summary">
                <div class="stat-card">
                    <div class="stat-number">$($websites.Count)</div>
                    <div>Websites Tested</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">4</div>
                    <div>API Providers</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">$($results.Count)</div>
                    <div>Total Tests</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">$([math]::Round($testDuration.TotalMinutes, 1))</div>
                    <div>Duration (min)</div>
                </div>
            </div>

            <div class="analysis">
                <h3>📊 Key Findings</h3>
                <ul>
                    <li><strong>ITDOG Issue</strong>: Multiple websites return identical latency values</li>
                    <li><strong>Globalping Best Performance</strong>: Correctly differentiates between domestic and foreign websites</li>
                    <li><strong>KeyCDN Stable</strong>: Consistent latency data, good as backup source</li>
                    <li><strong>VPN Impact</strong>: Local ping results affected by VPN interference</li>
                </ul>
            </div>

            <table>
                <thead>
                    <tr>
                        <th>Website</th>
                        <th>Category</th>
                        <th>API Provider</th>
                        <th>Status</th>
                        <th>Avg Latency (ms)</th>
                        <th>Min Latency (ms)</th>
                        <th>Max Latency (ms)</th>
                        <th>Nodes</th>
                        <th>Test Time</th>
                    </tr>
                </thead>
                <tbody>
"@

foreach ($result in $results) {
    $rowClass = switch ($result.Category) {
        "Domestic" { "domestic" }
        "Accessible" { "accessible" }
        "Blocked" { "blocked" }
        default { "" }
    }

    $statusClass = if ($result.Success) { "success" } else { "failed" }
    $statusText = if ($result.Success) { "✅ Success" } else { "❌ Failed" }

    $latencyClass = if ($result.Success -and $result.AvgLatency -ne "N/A") {
        if ($result.AvgLatency -lt 50) { "excellent" }
        elseif ($result.AvgLatency -lt 150) { "good" }
        else { "poor" }
    } else { "" }

    $categoryIcon = switch ($result.Category) {
        "Domestic" { "🇨🇳" }
        "Accessible" { "🌍" }
        "Blocked" { "🚫" }
        default { "" }
    }

    $htmlContent += @"
                    <tr class="$rowClass">
                        <td><strong>$($result.WebsiteName)</strong></td>
                        <td>$categoryIcon $($result.Category)</td>
                        <td>$($result.Provider)</td>
                        <td class="$statusClass">$statusText</td>
                        <td class="$latencyClass">$($result.AvgLatency)</td>
                        <td>$($result.MinLatency)</td>
                        <td>$($result.MaxLatency)</td>
                        <td>$($result.NodeCount)</td>
                        <td>$($result.TestTime)</td>
                    </tr>
"@
}

$htmlContent += @"
                </tbody>
            </table>
        </div>

        <div class="footer">
            <p>Report Generated: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss') | Test Tool: PowerShell + Multiple API Providers</p>
        </div>
    </div>
</body>
</html>
"@

# Save files
$htmlPath = "$env:USERPROFILE\Desktop\API-Providers-Latency-Test-Report.html"
$csvPath = "$env:USERPROFILE\Desktop\API-Providers-Latency-Test-Data.csv"

$htmlContent | Out-File -FilePath $htmlPath -Encoding UTF8
$results | Export-Csv -Path $csvPath -Encoding UTF8 -NoTypeInformation

Write-Host ""
Write-Host "Reports generated successfully!" -ForegroundColor Green
Write-Host "HTML Report: $htmlPath" -ForegroundColor Cyan
Write-Host "CSV Data: $csvPath" -ForegroundColor Cyan
Write-Host ""
Write-Host "Test completed! Duration: $([math]::Round($testDuration.TotalMinutes, 1)) minutes" -ForegroundColor Green

# Open HTML report
Start-Process $htmlPath
