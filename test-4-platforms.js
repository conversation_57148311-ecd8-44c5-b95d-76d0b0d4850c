// 测试4个平台并发测试功能
const { performMultiCloudPing, performComprehensivePing } = require('./src/services/PingService');

async function test4Platforms() {
  console.log('🚀 开始测试4个平台并发功能...\n');
  
  const testTargets = [
    'baidu.com',
    'taobao.com', 
    'qq.com',
    'example.com'
  ];

  for (const target of testTargets) {
    console.log(`\n📊 测试目标: ${target}`);
    console.log('=' .repeat(50));
    
    try {
      // 测试主要的ping函数
      console.log('\n🔄 执行 performMultiCloudPing...');
      const result1 = await performMultiCloudPing(target);
      
      if (result1.success) {
        console.log(`✅ performMultiCloudPing 成功`);
        console.log(`📈 获得节点数: ${result1.results.length}`);
        
        // 显示各平台的结果统计
        const platformStats = {};
        result1.results.forEach(result => {
          const source = result.apiSource || 'Unknown';
          if (!platformStats[source]) {
            platformStats[source] = 0;
          }
          platformStats[source]++;
        });
        
        console.log('\n📊 各平台节点统计:');
        Object.entries(platformStats).forEach(([platform, count]) => {
          console.log(`   ${platform}: ${count} 个节点`);
        });
        
        // 显示延迟范围
        const pings = result1.results.map(r => r.ping).filter(p => p > 0);
        if (pings.length > 0) {
          const minPing = Math.min(...pings);
          const maxPing = Math.max(...pings);
          const avgPing = Math.round(pings.reduce((a, b) => a + b, 0) / pings.length);
          console.log(`⚡ 延迟范围: ${minPing}ms - ${maxPing}ms (平均: ${avgPing}ms)`);
        }
      } else {
        console.log(`❌ performMultiCloudPing 失败: ${result1.error || '未知错误'}`);
      }
      
      // 测试综合ping函数
      console.log('\n🔄 执行 performComprehensivePing...');
      const result2 = await performComprehensivePing(target);
      
      if (result2.success) {
        console.log(`✅ performComprehensivePing 成功`);
        console.log(`📈 获得节点数: ${result2.results.length}`);
        
        // 显示API分解统计
        console.log('\n📊 API分解统计:');
        Object.entries(result2.apiBreakdown).forEach(([api, results]) => {
          console.log(`   ${api}: ${results.length} 个节点`);
        });
      } else {
        console.log(`❌ performComprehensivePing 失败: ${result2.error || '未知错误'}`);
      }
      
    } catch (error) {
      console.error(`❌ 测试 ${target} 时出错:`, error.message);
    }
    
    // 等待一下再测试下一个
    await new Promise(resolve => setTimeout(resolve, 2000));
  }
  
  console.log('\n🎉 4个平台并发测试完成！');
  console.log('\n📋 测试总结:');
  console.log('✅ 招商银行 - 金融网络测试');
  console.log('✅ 爱奇艺 - 视频CDN测试'); 
  console.log('✅ 百度网盘 - 存储网络测试');
  console.log('✅ 阿里云盘 - 云存储测试');
}

// 如果直接运行此脚本
if (require.main === module) {
  test4Platforms().catch(console.error);
}

module.exports = { test4Platforms };
