<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>全球节点测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
        .node-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        .node-item {
            background: white;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ddd;
            text-align: center;
        }
        .node-name {
            font-weight: bold;
            color: #333;
        }
        .node-info {
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .stats {
            display: flex;
            gap: 20px;
            margin: 15px 0;
            flex-wrap: wrap;
        }
        .stat-item {
            background: #e9ecef;
            padding: 10px 15px;
            border-radius: 4px;
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #666;
        }
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌍 全球节点测试</h1>
        <p>测试全球节点API，验证节点数量和覆盖范围</p>
        
        <div>
            <button class="test-button" onclick="testGlobalNodes('https://www.google.com')">
                测试 Google.com
            </button>
            <button class="test-button" onclick="testGlobalNodes('https://www.baidu.com')">
                测试 Baidu.com
            </button>
            <button class="test-button" onclick="testGlobalNodes('https://github.com')">
                测试 GitHub.com
            </button>
        </div>
        
        <div id="results"></div>
    </div>

    <script>
        async function testGlobalNodes(url) {
            const resultsEl = document.getElementById('results');
            resultsEl.innerHTML = '<div class="loading">🔄 正在测试全球节点...</div>';
            
            try {
                const response = await fetch('/api/ping-global', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        target: url,
                        maxNodes: 120
                    })
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                displayResults(url, data);
                
            } catch (error) {
                resultsEl.innerHTML = `
                    <div class="results">
                        <h3>❌ 测试失败</h3>
                        <p>错误信息: ${error.message}</p>
                    </div>
                `;
            }
        }
        
        function displayResults(url, data) {
            const resultsEl = document.getElementById('results');
            
            if (!data.success || !data.results) {
                resultsEl.innerHTML = `
                    <div class="results">
                        <h3>❌ 测试失败</h3>
                        <p>API返回错误或无数据</p>
                    </div>
                `;
                return;
            }
            
            const results = data.results;
            const metadata = data.metadata || {};
            
            // 按大洲分组
            const continentGroups = {};
            const providerGroups = {};
            
            results.forEach(result => {
                const continent = result.continent || '未知';
                const provider = result.provider || '未知';
                
                if (!continentGroups[continent]) {
                    continentGroups[continent] = [];
                }
                continentGroups[continent].push(result);
                
                if (!providerGroups[provider]) {
                    providerGroups[provider] = 0;
                }
                providerGroups[provider]++;
            });
            
            // 查找Vercel和Cloudflare节点
            const vercelNodes = results.filter(r => 
                r.node.includes('Vercel') || r.provider.includes('Vercel')
            );
            const cloudflareNodes = results.filter(r => 
                r.node.includes('Cloudflare') || r.provider.includes('Cloudflare')
            );
            const edgeNodes = results.filter(r => 
                r.node.includes('Edge') || r.provider.includes('Edge')
            );
            
            const html = `
                <div class="results">
                    <h3>📊 ${url} 全球节点测试结果</h3>
                    
                    <div class="stats">
                        <div class="stat-item">
                            <div class="stat-number">${results.length}</div>
                            <div class="stat-label">总节点数</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${Object.keys(continentGroups).length}</div>
                            <div class="stat-label">覆盖大洲</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${Object.keys(providerGroups).length}</div>
                            <div class="stat-label">服务提供商</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${vercelNodes.length}</div>
                            <div class="stat-label">Vercel节点</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${cloudflareNodes.length}</div>
                            <div class="stat-label">Cloudflare节点</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-number">${edgeNodes.length}</div>
                            <div class="stat-label">Edge节点</div>
                        </div>
                    </div>
                    
                    <h4>🌍 按大洲分布</h4>
                    ${Object.entries(continentGroups).map(([continent, nodes]) => `
                        <details style="margin: 10px 0;">
                            <summary style="cursor: pointer; font-weight: bold; padding: 5px;">
                                ${continent} (${nodes.length}个节点)
                            </summary>
                            <div class="node-grid">
                                ${nodes.map(node => `
                                    <div class="node-item">
                                        <div class="node-name">${node.node}</div>
                                        <div class="node-info">
                                            ${node.country}<br>
                                            ${node.ping}ms - ${node.provider}
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </details>
                    `).join('')}
                    
                    <h4>🔧 服务提供商分布</h4>
                    <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                        ${Object.entries(providerGroups).map(([provider, count]) => `
                            <div class="stat-item">
                                <div class="stat-number">${count}</div>
                                <div class="stat-label">${provider}</div>
                            </div>
                        `).join('')}
                    </div>
                    
                    <p style="margin-top: 15px; color: #666; font-size: 14px;">
                        测试时间: ${new Date().toLocaleString()}<br>
                        平均延迟: ${metadata.avgLatency || 'N/A'}ms<br>
                        成功率: ${metadata.successRate || 'N/A'}%
                    </p>
                </div>
            `;
            
            resultsEl.innerHTML = html;
        }
    </script>
</body>
</html>
