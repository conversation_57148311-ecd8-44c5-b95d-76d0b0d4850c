'use client';

import React, { useMemo } from 'react';
import { Bar<PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts';

// 定义ping结果接口
interface PingResult {
  platform: string;
  latency: number | null;
  status: 'pending' | 'success' | 'error' | 'timeout';
  error?: string;
  timestamp?: string;
}

// 定义平台配置接口
interface PlatformConfig {
  id: string;
  name: string;
  color: string;
  icon: string;
}

// 图表组件属性
interface LatencyChartProps {
  results: Record<string, PingResult>;
  platforms: PlatformConfig[];
  targetUrl: string;
  isDarkMode: boolean;
  chartType?: 'bar' | 'line' | 'pie';
  onChartTypeChange?: (type: 'bar' | 'line' | 'pie') => void;
}

// 延迟等级颜色配置
const LATENCY_COLORS = {
  excellent: '#10B981', // 绿色 - 优秀 (<50ms)
  good: '#3B82F6',      // 蓝色 - 良好 (50-100ms)
  fair: '#F59E0B',      // 黄色 - 一般 (100-200ms)
  poor: '#EF4444',      // 红色 - 较慢 (>200ms)
  error: '#6B7280'      // 灰色 - 错误
};

// 获取延迟等级
const getLatencyLevel = (latency: number | null): keyof typeof LATENCY_COLORS => {
  if (latency === null) return 'error';
  if (latency < 50) return 'excellent';
  if (latency < 100) return 'good';
  if (latency < 200) return 'fair';
  return 'poor';
};

// 获取延迟等级文本
const getLatencyLevelText = (latency: number | null): string => {
  if (latency === null) return '错误';
  if (latency < 50) return '优秀';
  if (latency < 100) return '良好';
  if (latency < 200) return '一般';
  return '较慢';
};

export default function LatencyChart({ results, platforms, targetUrl, isDarkMode, chartType = 'bar', onChartTypeChange }: LatencyChartProps) {
  // 准备图表数据
  const chartData = useMemo(() => {
    return platforms.map(platform => {
      const result = results[platform.id];
      const latency = result?.status === 'success' ? result.latency : null;
      
      return {
        platform: platform.name,
        platformId: platform.id,
        latency: latency || 0,
        actualLatency: latency,
        status: result?.status || 'pending',
        error: result?.error,
        color: platform.color,
        icon: platform.icon,
        level: getLatencyLevel(latency),
        levelText: getLatencyLevelText(latency)
      };
    }).filter(item => item.status !== 'pending'); // 过滤掉还在测试中的项目
  }, [results, platforms]);

  // 准备饼图数据
  const pieData = useMemo(() => {
    const levelCounts = chartData.reduce((acc, item) => {
      const level = item.level;
      acc[level] = (acc[level] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return Object.entries(levelCounts).map(([level, count]) => ({
      name: getLatencyLevelText(level === 'excellent' ? 25 : level === 'good' ? 75 : level === 'fair' ? 150 : level === 'poor' ? 250 : null),
      value: count,
      color: LATENCY_COLORS[level as keyof typeof LATENCY_COLORS]
    }));
  }, [chartData]);

  // 自定义工具提示
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div className={`p-3 rounded-lg shadow-lg border ${isDarkMode ? 'bg-gray-800 border-gray-600' : 'bg-white border-gray-200'}`}>
          <p className="font-medium mb-1">{data.icon} {label}</p>
          {data.status === 'success' ? (
            <>
              <p className="text-sm">延迟: <span className="font-mono font-bold">{data.actualLatency}ms</span></p>
              <p className="text-sm">等级: <span style={{ color: LATENCY_COLORS[data.level] }}>{data.levelText}</span></p>
            </>
          ) : (
            <p className="text-sm text-red-500">状态: {data.status === 'error' ? '测试失败' : '超时'}</p>
          )}
          {data.error && <p className="text-xs text-gray-500 mt-1">{data.error}</p>}
        </div>
      );
    }
    return null;
  };

  // 如果没有数据，显示空状态
  if (chartData.length === 0) {
    return (
      <div className={`p-8 text-center rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
        <div className="text-4xl mb-4">📊</div>
        <p className="text-lg font-medium mb-2">暂无测试数据</p>
        <p className="text-sm text-gray-500">请先运行ping测试以查看延迟对比图表</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* 图表标题 */}
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-2">📊 延迟对比分析</h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          目标: {new URL(targetUrl).hostname} | 测试时间: {new Date().toLocaleString('zh-CN')}
        </p>
      </div>

      {/* 图表切换按钮 */}
      <div className="flex justify-center space-x-2">
        {(['bar', 'line', 'pie'] as const).map(type => (
          <button
            key={type}
            onClick={() => onChartTypeChange?.(type)}
            className={`px-3 py-1 rounded text-sm transition-colors ${
              chartType === type
                ? 'bg-blue-500 text-white'
                : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
            }`}
          >
            {type === 'bar' ? '📊 柱状图' : type === 'line' ? '📈 折线图' : '🥧 饼图'}
          </button>
        ))}
      </div>

      {/* 图表容器 */}
      <div className="h-80">
        <ResponsiveContainer width="100%" height="100%">
          {chartType === 'bar' ? (
            <BarChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke={isDarkMode ? '#374151' : '#E5E7EB'} />
              <XAxis 
                dataKey="platform" 
                tick={{ fill: isDarkMode ? '#D1D5DB' : '#374151', fontSize: 12 }}
                axisLine={{ stroke: isDarkMode ? '#6B7280' : '#9CA3AF' }}
              />
              <YAxis 
                tick={{ fill: isDarkMode ? '#D1D5DB' : '#374151', fontSize: 12 }}
                axisLine={{ stroke: isDarkMode ? '#6B7280' : '#9CA3AF' }}
                label={{ value: '延迟 (ms)', angle: -90, position: 'insideLeft', style: { textAnchor: 'middle', fill: isDarkMode ? '#D1D5DB' : '#374151' } }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Bar 
                dataKey="latency" 
                fill={(entry: any) => LATENCY_COLORS[entry.level]}
                radius={[4, 4, 0, 0]}
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={LATENCY_COLORS[entry.level]} />
                ))}
              </Bar>
            </BarChart>
          ) : chartType === 'line' ? (
            <LineChart data={chartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" stroke={isDarkMode ? '#374151' : '#E5E7EB'} />
              <XAxis 
                dataKey="platform" 
                tick={{ fill: isDarkMode ? '#D1D5DB' : '#374151', fontSize: 12 }}
                axisLine={{ stroke: isDarkMode ? '#6B7280' : '#9CA3AF' }}
              />
              <YAxis 
                tick={{ fill: isDarkMode ? '#D1D5DB' : '#374151', fontSize: 12 }}
                axisLine={{ stroke: isDarkMode ? '#6B7280' : '#9CA3AF' }}
                label={{ value: '延迟 (ms)', angle: -90, position: 'insideLeft', style: { textAnchor: 'middle', fill: isDarkMode ? '#D1D5DB' : '#374151' } }}
              />
              <Tooltip content={<CustomTooltip />} />
              <Line 
                type="monotone" 
                dataKey="latency" 
                stroke="#3B82F6" 
                strokeWidth={3}
                dot={{ fill: '#3B82F6', strokeWidth: 2, r: 6 }}
                activeDot={{ r: 8, stroke: '#3B82F6', strokeWidth: 2 }}
              />
            </LineChart>
          ) : (
            <PieChart>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, value, percent }) => `${name}: ${value} (${(percent * 100).toFixed(0)}%)`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {pieData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          )}
        </ResponsiveContainer>
      </div>

      {/* 统计信息 */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {(() => {
          const successResults = chartData.filter(item => item.status === 'success' && item.actualLatency !== null);
          const latencies = successResults.map(item => item.actualLatency!);
          const avgLatency = latencies.length > 0 ? Math.round(latencies.reduce((a, b) => a + b, 0) / latencies.length) : 0;
          const minLatency = latencies.length > 0 ? Math.min(...latencies) : 0;
          const maxLatency = latencies.length > 0 ? Math.max(...latencies) : 0;
          const successRate = chartData.length > 0 ? Math.round((successResults.length / chartData.length) * 100) : 0;

          return (
            <>
              <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="text-xl font-bold text-blue-600">{avgLatency}ms</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">平均延迟</div>
              </div>
              <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <div className="text-xl font-bold text-green-600">{minLatency}ms</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">最低延迟</div>
              </div>
              <div className="text-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                <div className="text-xl font-bold text-red-600">{maxLatency}ms</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">最高延迟</div>
              </div>
              <div className="text-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                <div className="text-xl font-bold text-purple-600">{successRate}%</div>
                <div className="text-sm text-gray-600 dark:text-gray-400">成功率</div>
              </div>
            </>
          );
        })()}
      </div>

      {/* 延迟等级说明 */}
      <div className={`p-4 rounded-lg ${isDarkMode ? 'bg-gray-800' : 'bg-gray-100'}`}>
        <h4 className="font-medium mb-3">📋 延迟等级说明</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          <div className="flex items-center">
            <div className="w-4 h-4 rounded mr-2" style={{ backgroundColor: LATENCY_COLORS.excellent }}></div>
            <span className="text-sm">优秀 (&lt;50ms)</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 rounded mr-2" style={{ backgroundColor: LATENCY_COLORS.good }}></div>
            <span className="text-sm">良好 (50-100ms)</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 rounded mr-2" style={{ backgroundColor: LATENCY_COLORS.fair }}></div>
            <span className="text-sm">一般 (100-200ms)</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 rounded mr-2" style={{ backgroundColor: LATENCY_COLORS.poor }}></div>
            <span className="text-sm">较慢 (&gt;200ms)</span>
          </div>
        </div>
      </div>
    </div>
  );
}
