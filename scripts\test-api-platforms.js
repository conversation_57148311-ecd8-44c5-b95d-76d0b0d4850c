#!/usr/bin/env node

/**
 * 测试API平台测试页面
 * 验证10个ping平台的API调用功能
 */

const fetch = require('node-fetch');

// 测试平台列表
const platforms = [
  { id: '17ce', name: '17CE.COM' },
  { id: 'chinaz', name: 'Chinaz站长工具' },
  { id: 'itdog', name: 'ITDOG.CN' },
  { id: 'boce', name: 'BOCE.COM' },
  { id: 'alibaba-boce', name: '阿里云BOCE' },
  { id: 'globalping', name: 'Globalping.io' },
  { id: 'ping-pe', name: 'Ping.pe' },
  { id: 'cloudflare-worker', name: 'Cloudflare Workers' },
  { id: 'vercel-edge', name: 'Vercel Edge Functions' },
  { id: 'multi-platform', name: 'Multi-Platform API' }
];

// 测试目标
const testTargets = ['google.com', 'baidu.com'];

async function testSinglePlatform(platform, target) {
  try {
    console.log(`🧪 测试 ${platform.name} - ${target}`);
    
    const response = await fetch('http://localhost:3001/api/test-ping-platforms', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        platform: platform.id,
        target: target,
        timeout: 10000
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();
    
    console.log(`  ✅ 成功: ${result.success}`);
    console.log(`  📊 节点数: ${result.nodeCount}`);
    console.log(`  ⏱️ 响应时间: ${result.responseTime}ms`);
    console.log(`  📈 数据质量: ${result.dataQuality}%`);
    console.log(`  🎯 准确性: ${result.accuracy}%`);
    
    if (result.error) {
      console.log(`  ❌ 错误: ${result.error}`);
    }
    
    return result;
  } catch (error) {
    console.log(`  ❌ 测试失败: ${error.message}`);
    return {
      platform: platform.name,
      success: false,
      error: error.message,
      responseTime: 0,
      nodeCount: 0,
      dataQuality: 0,
      accuracy: 0
    };
  }
}

async function runTests() {
  console.log('🚀 开始测试API平台...\n');
  console.log(`📋 测试平台数量: ${platforms.length}`);
  console.log(`🎯 测试目标: ${testTargets.join(', ')}\n`);

  const results = [];

  for (const target of testTargets) {
    console.log(`\n🌐 测试目标: ${target}`);
    console.log('='.repeat(50));

    for (const platform of platforms) {
      const result = await testSinglePlatform(platform, target);
      results.push({
        target,
        platform: platform.name,
        ...result
      });
      
      // 短暂延迟避免过于频繁的请求
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }

  // 统计结果
  console.log('\n📊 测试结果统计');
  console.log('='.repeat(50));
  
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  const avgResponseTime = results.reduce((sum, r) => sum + r.responseTime, 0) / results.length;
  
  console.log(`✅ 成功: ${successful}/${results.length}`);
  console.log(`❌ 失败: ${failed}/${results.length}`);
  console.log(`📈 成功率: ${((successful / results.length) * 100).toFixed(1)}%`);
  console.log(`⏱️ 平均响应时间: ${Math.round(avgResponseTime)}ms`);

  // 按平台分组统计
  console.log('\n🏆 平台表现排名');
  console.log('='.repeat(50));
  
  const platformStats = {};
  platforms.forEach(platform => {
    const platformResults = results.filter(r => r.platform === platform.name);
    const successCount = platformResults.filter(r => r.success).length;
    const avgTime = platformResults.reduce((sum, r) => sum + r.responseTime, 0) / platformResults.length;
    
    platformStats[platform.name] = {
      successRate: (successCount / platformResults.length) * 100,
      avgResponseTime: Math.round(avgTime),
      successCount,
      totalTests: platformResults.length
    };
  });

  // 按成功率排序
  const sortedPlatforms = Object.entries(platformStats)
    .sort(([,a], [,b]) => b.successRate - a.successRate);

  sortedPlatforms.forEach(([name, stats], index) => {
    const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : '  ';
    console.log(`${medal} ${name}`);
    console.log(`    成功率: ${stats.successRate.toFixed(1)}% (${stats.successCount}/${stats.totalTests})`);
    console.log(`    平均响应时间: ${stats.avgResponseTime}ms`);
  });

  console.log('\n🎯 推荐使用的平台:');
  const topPlatforms = sortedPlatforms.slice(0, 3);
  topPlatforms.forEach(([name, stats], index) => {
    console.log(`${index + 1}. ${name} - 成功率 ${stats.successRate.toFixed(1)}%`);
  });

  console.log('\n✅ 测试完成！');
}

// 运行测试
if (require.main === module) {
  runTests().catch(console.error);
}

module.exports = { testSinglePlatform, runTests };
