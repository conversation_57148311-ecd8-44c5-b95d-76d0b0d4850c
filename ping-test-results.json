{"testSummary": {"totalTests": 80, "successfulTests": 79, "testDuration": 439637, "timestamp": "2025-07-16T04:30:01.092Z"}, "apiRanking": [{"api": "globalping", "successRate": "100.0%", "averageNodes": 8, "averagePing": 71, "averageExecutionTime": 7668}, {"api": "17ce", "successRate": "100.0%", "averageNodes": 4, "averagePing": 145, "averageExecutionTime": 601}, {"api": "boce", "successRate": "100.0%", "averageNodes": 3, "averagePing": 42, "averageExecutionTime": 354}, {"api": "aliyun-boce", "successRate": "100.0%", "averageNodes": 3, "averagePing": 159, "averageExecutionTime": 291}, {"api": "uptimerobot", "successRate": "100.0%", "averageNodes": 3, "averagePing": 0, "averageExecutionTime": 5}, {"api": "multi-reliable", "successRate": "100.0%", "averageNodes": 6, "averagePing": 371, "averageExecutionTime": 7022}, {"api": "enhanced", "successRate": "100.0%", "averageNodes": 7, "averagePing": 231, "averageExecutionTime": 18662}, {"api": "global-test", "successRate": "100.0%", "averageNodes": 25, "averagePing": 132, "averageExecutionTime": 9}], "siteAnalysis": [{"site": "163.com", "siteType": "domestic", "averagePing": 103, "minPing": 0, "maxPing": 252, "variance": 80, "bestAPI": "uptimerobot", "worstAPI": "multi-reliable"}, {"site": "baidu.com", "siteType": "domestic", "averagePing": 107, "minPing": 0, "maxPing": 274, "variance": 85, "bestAPI": "uptimerobot", "worstAPI": "multi-reliable"}, {"site": "qq.com", "siteType": "domestic", "averagePing": 117, "minPing": 0, "maxPing": 297, "variance": 102, "bestAPI": "uptimerobot", "worstAPI": "multi-reliable"}, {"site": "facebook.com", "siteType": "international", "averagePing": 131, "minPing": 0, "maxPing": 218, "variance": 92, "bestAPI": "uptimerobot", "worstAPI": "enhanced"}, {"site": "youtube.com", "siteType": "international", "averagePing": 135, "minPing": 0, "maxPing": 234, "variance": 97, "bestAPI": "uptimerobot", "worstAPI": "17ce"}, {"site": "google.com", "siteType": "international", "averagePing": 137, "minPing": 0, "maxPing": 272, "variance": 98, "bestAPI": "uptimerobot", "worstAPI": "aliyun-boce"}, {"site": "x.com", "siteType": "international", "averagePing": 142, "minPing": 0, "maxPing": 263, "variance": 94, "bestAPI": "uptimerobot", "worstAPI": "aliyun-boce"}, {"site": "instagram.com", "siteType": "international", "averagePing": 147, "minPing": 0, "maxPing": 224, "variance": 85, "bestAPI": "uptimerobot", "worstAPI": "aliyun-boce"}, {"site": "sina.com.cn", "siteType": "domestic", "averagePing": 154, "minPing": 0, "maxPing": 541, "variance": 174, "bestAPI": "uptimerobot", "worstAPI": "multi-reliable"}, {"site": "wobshare.us.kg", "siteType": "domestic", "averagePing": 276, "minPing": 0, "maxPing": 1310, "variance": 401, "bestAPI": "uptimerobot", "worstAPI": "multi-reliable"}], "rawResults": [{"endpoint": "/api/ping-globalping", "target": "wobshare.us.kg", "success": true, "totalNodes": 8, "successfulNodes": 8, "averagePing": 23, "minPing": 0.696, "maxPing": 86.626, "provider": "globalping", "dataSource": "Globalping.io", "executionTime": 6792, "siteType": "domestic"}, {"endpoint": "/api/ping-17ce", "target": "wobshare.us.kg", "success": true, "totalNodes": 4, "successfulNodes": 4, "averagePing": 188, "minPing": 174, "maxPing": 196, "provider": "17CE.COM", "dataSource": "17CE.COM (Backup)", "executionTime": 437, "siteType": "domestic"}, {"endpoint": "/api/ping-boce", "target": "wobshare.us.kg", "success": true, "totalNodes": 3, "successfulNodes": 3, "averagePing": 37, "minPing": 25, "maxPing": 46, "provider": "BOCE.COM", "dataSource": "BOCE.COM (模拟数据，因为网页抓取不稳定)", "executionTime": 395, "siteType": "domestic"}, {"endpoint": "/api/ping-aliyun-boce", "target": "wobshare.us.kg", "success": true, "totalNodes": 3, "successfulNodes": 3, "averagePing": 227, "minPing": 204, "maxPing": 256, "provider": "阿里云BOCE", "dataSource": "阿里云BOCE (模拟数据，因为网页抓取不稳定)", "executionTime": 472, "siteType": "domestic"}, {"endpoint": "/api/ping-uptimerobot", "target": "wobshare.us.kg", "success": true, "totalNodes": 3, "successfulNodes": 0, "averagePing": 0, "minPing": 0, "maxPing": 0, "provider": "UptimeRobot", "dataSource": "Unknown", "executionTime": 6, "siteType": "domestic"}, {"endpoint": "/api/ping-multi-reliable", "target": "wobshare.us.kg", "success": true, "totalNodes": 1, "successfulNodes": 1, "averagePing": 1310, "minPing": 1310, "maxPing": 1310, "provider": "Multi-Reliable", "dataSource": "Multi-Reliable API", "executionTime": 3091, "siteType": "domestic"}, {"endpoint": "/api/ping-enhanced", "target": "wobshare.us.kg", "success": true, "totalNodes": 2, "successfulNodes": 2, "averagePing": 253, "minPing": 232, "maxPing": 274, "provider": "Enhanced <PERSON>", "dataSource": "Enhanced Ping API", "executionTime": 11128, "siteType": "domestic"}, {"endpoint": "/api/ping-global-test", "target": "wobshare.us.kg", "success": true, "totalNodes": 25, "successfulNodes": 19, "averagePing": 166, "minPing": 32, "maxPing": 406, "provider": "Global Test", "dataSource": "Global Test Network", "executionTime": 9, "siteType": "domestic"}, {"endpoint": "/api/ping-globalping", "target": "baidu.com", "success": true, "totalNodes": 8, "successfulNodes": 8, "averagePing": 149, "minPing": 36.704, "maxPing": 241.776, "provider": "globalping", "dataSource": "Globalping.io", "executionTime": 5892, "siteType": "domestic"}, {"endpoint": "/api/ping-17ce", "target": "baidu.com", "success": true, "totalNodes": 4, "successfulNodes": 4, "averagePing": 50, "minPing": 33, "maxPing": 77, "provider": "17CE.COM", "dataSource": "17CE.COM (Backup)", "executionTime": 416, "siteType": "domestic"}, {"endpoint": "/api/ping-boce", "target": "baidu.com", "success": true, "totalNodes": 3, "successfulNodes": 3, "averagePing": 52, "minPing": 43, "maxPing": 68, "provider": "BOCE.COM", "dataSource": "BOCE.COM (模拟数据，因为网页抓取不稳定)", "executionTime": 573, "siteType": "domestic"}, {"endpoint": "/api/ping-aliyun-boce", "target": "baidu.com", "success": true, "totalNodes": 3, "successfulNodes": 3, "averagePing": 44, "minPing": 32, "maxPing": 50, "provider": "阿里云BOCE", "dataSource": "阿里云BOCE (模拟数据，因为网页抓取不稳定)", "executionTime": 272, "siteType": "domestic"}, {"endpoint": "/api/ping-uptimerobot", "target": "baidu.com", "success": true, "totalNodes": 3, "successfulNodes": 0, "averagePing": 0, "minPing": 0, "maxPing": 0, "provider": "UptimeRobot", "dataSource": "Unknown", "executionTime": 6, "siteType": "domestic"}, {"endpoint": "/api/ping-multi-reliable", "target": "baidu.com", "success": true, "totalNodes": 1, "successfulNodes": 1, "averagePing": 274, "minPing": 274, "maxPing": 274, "provider": "Multi-Reliable", "dataSource": "Multi-Reliable API", "executionTime": 1616, "siteType": "domestic"}, {"endpoint": "/api/ping-enhanced", "target": "baidu.com", "success": true, "totalNodes": 2, "successfulNodes": 2, "averagePing": 183, "minPing": 161, "maxPing": 204, "provider": "Enhanced <PERSON>", "dataSource": "Enhanced Ping API", "executionTime": 10613, "siteType": "domestic"}, {"endpoint": "/api/ping-global-test", "target": "baidu.com", "success": true, "totalNodes": 25, "successfulNodes": 25, "averagePing": 100, "minPing": 25, "maxPing": 223, "provider": "Global Test", "dataSource": "Global Test Network", "executionTime": 8, "siteType": "domestic"}, {"endpoint": "/api/ping-globalping", "target": "qq.com", "success": true, "totalNodes": 8, "successfulNodes": 8, "averagePing": 130, "minPing": 28.563, "maxPing": 254.142, "provider": "globalping", "dataSource": "Globalping.io", "executionTime": 4563, "siteType": "domestic"}, {"endpoint": "/api/ping-17ce", "target": "qq.com", "success": true, "totalNodes": 4, "successfulNodes": 4, "averagePing": 53, "minPing": 34, "maxPing": 64, "provider": "17CE.COM", "dataSource": "17CE.COM (Backup)", "executionTime": 470, "siteType": "domestic"}, {"endpoint": "/api/ping-boce", "target": "qq.com", "success": true, "totalNodes": 3, "successfulNodes": 3, "averagePing": 50, "minPing": 21, "maxPing": 72, "provider": "BOCE.COM", "dataSource": "BOCE.COM (模拟数据，因为网页抓取不稳定)", "executionTime": 319, "siteType": "domestic"}, {"endpoint": "/api/ping-aliyun-boce", "target": "qq.com", "success": true, "totalNodes": 3, "successfulNodes": 3, "averagePing": 45, "minPing": 32, "maxPing": 57, "provider": "阿里云BOCE", "dataSource": "阿里云BOCE (模拟数据，因为网页抓取不稳定)", "executionTime": 248, "siteType": "domestic"}, {"endpoint": "/api/ping-uptimerobot", "target": "qq.com", "success": true, "totalNodes": 3, "successfulNodes": 0, "averagePing": 0, "minPing": 0, "maxPing": 0, "provider": "UptimeRobot", "dataSource": "Unknown", "executionTime": 4, "siteType": "domestic"}, {"endpoint": "/api/ping-multi-reliable", "target": "qq.com", "success": true, "totalNodes": 1, "successfulNodes": 1, "averagePing": 297, "minPing": 297, "maxPing": 297, "provider": "Multi-Reliable", "dataSource": "Multi-Reliable API", "executionTime": 1912, "siteType": "domestic"}, {"endpoint": "/api/ping-enhanced", "target": "qq.com", "success": true, "totalNodes": 2, "successfulNodes": 2, "averagePing": 267, "minPing": 261, "maxPing": 273, "provider": "Enhanced <PERSON>", "dataSource": "Enhanced Ping API", "executionTime": 11139, "siteType": "domestic"}, {"endpoint": "/api/ping-global-test", "target": "qq.com", "success": true, "totalNodes": 25, "successfulNodes": 24, "averagePing": 91, "minPing": 23, "maxPing": 208, "provider": "Global Test", "dataSource": "Global Test Network", "executionTime": 7, "siteType": "domestic"}, {"endpoint": "/api/ping-globalping", "target": "sina.com.cn", "success": true, "totalNodes": 8, "successfulNodes": 8, "averagePing": 129, "minPing": 33.519, "maxPing": 223.582, "provider": "globalping", "dataSource": "Globalping.io", "executionTime": 5918, "siteType": "domestic"}, {"endpoint": "/api/ping-17ce", "target": "sina.com.cn", "success": true, "totalNodes": 4, "successfulNodes": 4, "averagePing": 53, "minPing": 41, "maxPing": 61, "provider": "17CE.COM", "dataSource": "17CE.COM (Backup)", "executionTime": 612, "siteType": "domestic"}, {"endpoint": "/api/ping-boce", "target": "sina.com.cn", "success": true, "totalNodes": 3, "successfulNodes": 3, "averagePing": 40, "minPing": 27, "maxPing": 47, "provider": "BOCE.COM", "dataSource": "BOCE.COM (模拟数据，因为网页抓取不稳定)", "executionTime": 309, "siteType": "domestic"}, {"endpoint": "/api/ping-aliyun-boce", "target": "sina.com.cn", "success": true, "totalNodes": 3, "successfulNodes": 3, "averagePing": 42, "minPing": 31, "maxPing": 58, "provider": "阿里云BOCE", "dataSource": "阿里云BOCE (模拟数据，因为网页抓取不稳定)", "executionTime": 310, "siteType": "domestic"}, {"endpoint": "/api/ping-uptimerobot", "target": "sina.com.cn", "success": true, "totalNodes": 3, "successfulNodes": 0, "averagePing": 0, "minPing": 0, "maxPing": 0, "provider": "UptimeRobot", "dataSource": "Unknown", "executionTime": 5, "siteType": "domestic"}, {"endpoint": "/api/ping-multi-reliable", "target": "sina.com.cn", "success": true, "totalNodes": 1, "successfulNodes": 1, "averagePing": 541, "minPing": 541, "maxPing": 541, "provider": "Multi-Reliable", "dataSource": "Multi-Reliable API", "executionTime": 1969, "siteType": "domestic"}, {"endpoint": "/api/ping-enhanced", "target": "sina.com.cn", "success": true, "totalNodes": 2, "successfulNodes": 2, "averagePing": 328, "minPing": 261, "maxPing": 395, "provider": "Enhanced <PERSON>", "dataSource": "Enhanced Ping API", "executionTime": 11120, "siteType": "domestic"}, {"endpoint": "/api/ping-global-test", "target": "sina.com.cn", "success": true, "totalNodes": 25, "successfulNodes": 23, "averagePing": 96, "minPing": 20, "maxPing": 222, "provider": "Global Test", "dataSource": "Global Test Network", "executionTime": 6, "siteType": "domestic"}, {"endpoint": "/api/ping-globalping", "target": "163.com", "success": true, "totalNodes": 8, "successfulNodes": 8, "averagePing": 139, "minPing": 11.639, "maxPing": 248.361, "provider": "globalping", "dataSource": "Globalping.io", "executionTime": 4213, "siteType": "domestic"}, {"endpoint": "/api/ping-17ce", "target": "163.com", "success": true, "totalNodes": 4, "successfulNodes": 4, "averagePing": 51, "minPing": 39, "maxPing": 71, "provider": "17CE.COM", "dataSource": "17CE.COM (Backup)", "executionTime": 555, "siteType": "domestic"}, {"endpoint": "/api/ping-boce", "target": "163.com", "success": true, "totalNodes": 3, "successfulNodes": 3, "averagePing": 42, "minPing": 33, "maxPing": 56, "provider": "BOCE.COM", "dataSource": "BOCE.COM (模拟数据，因为网页抓取不稳定)", "executionTime": 332, "siteType": "domestic"}, {"endpoint": "/api/ping-aliyun-boce", "target": "163.com", "success": true, "totalNodes": 3, "successfulNodes": 3, "averagePing": 46, "minPing": 23, "maxPing": 57, "provider": "阿里云BOCE", "dataSource": "阿里云BOCE (模拟数据，因为网页抓取不稳定)", "executionTime": 250, "siteType": "domestic"}, {"endpoint": "/api/ping-uptimerobot", "target": "163.com", "success": true, "totalNodes": 3, "successfulNodes": 0, "averagePing": 0, "minPing": 0, "maxPing": 0, "provider": "UptimeRobot", "dataSource": "Unknown", "executionTime": 5, "siteType": "domestic"}, {"endpoint": "/api/ping-multi-reliable", "target": "163.com", "success": true, "totalNodes": 1, "successfulNodes": 1, "averagePing": 252, "minPing": 252, "maxPing": 252, "provider": "Multi-Reliable", "dataSource": "Multi-Reliable API", "executionTime": 1495, "siteType": "domestic"}, {"endpoint": "/api/ping-enhanced", "target": "163.com", "success": true, "totalNodes": 2, "successfulNodes": 2, "averagePing": 192, "minPing": 177, "maxPing": 206, "provider": "Enhanced <PERSON>", "dataSource": "Enhanced Ping API", "executionTime": 10635, "siteType": "domestic"}, {"endpoint": "/api/ping-global-test", "target": "163.com", "success": true, "totalNodes": 25, "successfulNodes": 21, "averagePing": 101, "minPing": 19, "maxPing": 282, "provider": "Global Test", "dataSource": "Global Test Network", "executionTime": 8, "siteType": "domestic"}, {"endpoint": "/api/ping-globalping", "target": "google.com", "success": true, "totalNodes": 8, "successfulNodes": 6, "averagePing": 3, "minPing": 0.901, "maxPing": 8.58, "provider": "globalping", "dataSource": "Globalping.io", "executionTime": 11647, "siteType": "international"}, {"endpoint": "/api/ping-17ce", "target": "google.com", "success": true, "totalNodes": 4, "successfulNodes": 4, "averagePing": 200, "minPing": 166, "maxPing": 227, "provider": "17CE.COM", "dataSource": "17CE.COM (Backup)", "executionTime": 404, "siteType": "international"}, {"endpoint": "/api/ping-boce", "target": "google.com", "success": true, "totalNodes": 3, "successfulNodes": 3, "averagePing": 54, "minPing": 41, "maxPing": 70, "provider": "BOCE.COM", "dataSource": "BOCE.COM (模拟数据，因为网页抓取不稳定)", "executionTime": 329, "siteType": "international"}, {"endpoint": "/api/ping-aliyun-boce", "target": "google.com", "success": true, "totalNodes": 3, "successfulNodes": 3, "averagePing": 272, "minPing": 238, "maxPing": 300, "provider": "阿里云BOCE", "dataSource": "阿里云BOCE (模拟数据，因为网页抓取不稳定)", "executionTime": 295, "siteType": "international"}, {"endpoint": "/api/ping-uptimerobot", "target": "google.com", "success": true, "totalNodes": 3, "successfulNodes": 0, "averagePing": 0, "minPing": 0, "maxPing": 0, "provider": "UptimeRobot", "dataSource": "Unknown", "executionTime": 5, "siteType": "international"}, {"endpoint": "/api/ping-multi-reliable", "target": "google.com", "success": true, "totalNodes": 10, "successfulNodes": 10, "averagePing": 205, "minPing": 127, "maxPing": 323, "provider": "Multi-Reliable", "dataSource": "Multi-Reliable API", "executionTime": 11715, "siteType": "international"}, {"endpoint": "/api/ping-enhanced", "target": "google.com", "success": true, "totalNodes": 12, "successfulNodes": 12, "averagePing": 218, "minPing": 160, "maxPing": 271, "provider": "Enhanced <PERSON>", "dataSource": "Enhanced Ping API", "executionTime": 26298, "siteType": "international"}, {"endpoint": "/api/ping-global-test", "target": "google.com", "success": true, "totalNodes": 25, "successfulNodes": 20, "averagePing": 147, "minPing": 29, "maxPing": 323, "provider": "Global Test", "dataSource": "Global Test Network", "executionTime": 25, "siteType": "international"}, {"endpoint": "/api/ping-globalping", "target": "youtube.com", "success": true, "totalNodes": 8, "successfulNodes": 6, "averagePing": 2, "minPing": 0.466, "maxPing": 5.236, "provider": "globalping", "dataSource": "Globalping.io", "executionTime": 12404, "siteType": "international"}, {"endpoint": "/api/ping-17ce", "target": "youtube.com", "success": true, "totalNodes": 4, "successfulNodes": 4, "averagePing": 234, "minPing": 206, "maxPing": 255, "provider": "17CE.COM", "dataSource": "17CE.COM (Backup)", "executionTime": 1490, "siteType": "international"}, {"endpoint": "/api/ping-boce", "target": "youtube.com", "success": true, "totalNodes": 3, "successfulNodes": 3, "averagePing": 32, "minPing": 22, "maxPing": 40, "provider": "BOCE.COM", "dataSource": "BOCE.COM (模拟数据，因为网页抓取不稳定)", "executionTime": 317, "siteType": "international"}, {"endpoint": "/api/ping-aliyun-boce", "target": "youtube.com", "success": true, "totalNodes": 3, "successfulNodes": 3, "averagePing": 216, "minPing": 203, "maxPing": 237, "provider": "阿里云BOCE", "dataSource": "阿里云BOCE (模拟数据，因为网页抓取不稳定)", "executionTime": 282, "siteType": "international"}, {"endpoint": "/api/ping-uptimerobot", "target": "youtube.com", "success": true, "totalNodes": 3, "successfulNodes": 0, "averagePing": 0, "minPing": 0, "maxPing": 0, "provider": "UptimeRobot", "dataSource": "Unknown", "executionTime": 5, "siteType": "international"}, {"endpoint": "/api/ping-multi-reliable", "target": "youtube.com", "success": true, "totalNodes": 10, "successfulNodes": 10, "averagePing": 204, "minPing": 134, "maxPing": 301, "provider": "Multi-Reliable", "dataSource": "Multi-Reliable API", "executionTime": 12387, "siteType": "international"}, {"endpoint": "/api/ping-enhanced", "target": "youtube.com", "success": true, "totalNodes": 12, "successfulNodes": 12, "averagePing": 219, "minPing": 149, "maxPing": 267, "provider": "Enhanced <PERSON>", "dataSource": "Enhanced Ping API", "executionTime": 26573, "siteType": "international"}, {"endpoint": "/api/ping-global-test", "target": "youtube.com", "success": true, "totalNodes": 25, "successfulNodes": 18, "averagePing": 169, "minPing": 48, "maxPing": 346, "provider": "Global Test", "dataSource": "Global Test Network", "executionTime": 6, "siteType": "international"}, {"endpoint": "/api/ping-globalping", "target": "facebook.com", "success": true, "totalNodes": 8, "successfulNodes": 6, "averagePing": 2, "minPing": 0.573, "maxPing": 5.193, "provider": "globalping", "dataSource": "Globalping.io", "executionTime": 12579, "siteType": "international"}, {"endpoint": "/api/ping-17ce", "target": "facebook.com", "success": true, "totalNodes": 4, "successfulNodes": 4, "averagePing": 199, "minPing": 175, "maxPing": 224, "provider": "17CE.COM", "dataSource": "17CE.COM (Backup)", "executionTime": 839, "siteType": "international"}, {"endpoint": "/api/ping-boce", "target": "facebook.com", "success": true, "totalNodes": 3, "successfulNodes": 3, "averagePing": 48, "minPing": 33, "maxPing": 63, "provider": "BOCE.COM", "dataSource": "BOCE.COM (模拟数据，因为网页抓取不稳定)", "executionTime": 313, "siteType": "international"}, {"endpoint": "/api/ping-aliyun-boce", "target": "facebook.com", "success": true, "totalNodes": 3, "successfulNodes": 3, "averagePing": 214, "minPing": 207, "maxPing": 223, "provider": "阿里云BOCE", "dataSource": "阿里云BOCE (模拟数据，因为网页抓取不稳定)", "executionTime": 261, "siteType": "international"}, {"endpoint": "/api/ping-uptimerobot", "target": "facebook.com", "success": true, "totalNodes": 3, "successfulNodes": 0, "averagePing": 0, "minPing": 0, "maxPing": 0, "provider": "UptimeRobot", "dataSource": "Unknown", "executionTime": 4, "siteType": "international"}, {"endpoint": "/api/ping-multi-reliable", "target": "facebook.com", "success": true, "totalNodes": 10, "successfulNodes": 10, "averagePing": 215, "minPing": 157, "maxPing": 357, "provider": "Multi-Reliable", "dataSource": "Multi-Reliable API", "executionTime": 11712, "siteType": "international"}, {"endpoint": "/api/ping-enhanced", "target": "facebook.com", "success": true, "totalNodes": 12, "successfulNodes": 12, "averagePing": 218, "minPing": 155, "maxPing": 292, "provider": "Enhanced <PERSON>", "dataSource": "Enhanced Ping API", "executionTime": 26492, "siteType": "international"}, {"endpoint": "/api/ping-global-test", "target": "facebook.com", "success": true, "totalNodes": 25, "successfulNodes": 18, "averagePing": 155, "minPing": 26, "maxPing": 332, "provider": "Global Test", "dataSource": "Global Test Network", "executionTime": 7, "siteType": "international"}, {"endpoint": "/api/ping-globalping", "target": "x.com", "success": true, "totalNodes": 8, "successfulNodes": 8, "averagePing": 58, "minPing": 0.959, "maxPing": 219.074, "provider": "globalping", "dataSource": "Globalping.io", "executionTime": 5004, "siteType": "international"}, {"endpoint": "/api/ping-17ce", "target": "x.com", "success": true, "totalNodes": 4, "successfulNodes": 4, "averagePing": 216, "minPing": 193, "maxPing": 242, "provider": "17CE.COM", "dataSource": "17CE.COM (Backup)", "executionTime": 376, "siteType": "international"}, {"endpoint": "/api/ping-boce", "target": "x.com", "success": true, "totalNodes": 3, "successfulNodes": 3, "averagePing": 30, "minPing": 25, "maxPing": 37, "provider": "BOCE.COM", "dataSource": "BOCE.COM (模拟数据，因为网页抓取不稳定)", "executionTime": 285, "siteType": "international"}, {"endpoint": "/api/ping-aliyun-boce", "target": "x.com", "success": true, "totalNodes": 3, "successfulNodes": 3, "averagePing": 263, "minPing": 252, "maxPing": 282, "provider": "阿里云BOCE", "dataSource": "阿里云BOCE (模拟数据，因为网页抓取不稳定)", "executionTime": 265, "siteType": "international"}, {"endpoint": "/api/ping-uptimerobot", "target": "x.com", "success": true, "totalNodes": 3, "successfulNodes": 0, "averagePing": 0, "minPing": 0, "maxPing": 0, "provider": "UptimeRobot", "dataSource": "Unknown", "executionTime": 5, "siteType": "international"}, {"endpoint": "/api/ping-multi-reliable", "target": "x.com", "success": true, "totalNodes": 10, "successfulNodes": 10, "averagePing": 215, "minPing": 156, "maxPing": 324, "provider": "Multi-Reliable", "dataSource": "Multi-Reliable API", "executionTime": 12387, "siteType": "international"}, {"endpoint": "/api/ping-enhanced", "target": "x.com", "success": true, "totalNodes": 12, "successfulNodes": 12, "averagePing": 212, "minPing": 148, "maxPing": 311, "provider": "Enhanced <PERSON>", "dataSource": "Enhanced Ping API", "executionTime": 26341, "siteType": "international"}, {"endpoint": "/api/ping-global-test", "target": "x.com", "success": true, "totalNodes": 25, "successfulNodes": 18, "averagePing": 138, "minPing": 39, "maxPing": 357, "provider": "Global Test", "dataSource": "Global Test Network", "executionTime": 8, "siteType": "international"}, {"endpoint": "/api/ping-globalping", "target": "instagram.com", "success": false, "error": "HTTP 500", "executionTime": 636, "siteType": "international"}, {"endpoint": "/api/ping-17ce", "target": "instagram.com", "success": true, "totalNodes": 4, "successfulNodes": 4, "averagePing": 202, "minPing": 164, "maxPing": 236, "provider": "17CE.COM", "dataSource": "17CE.COM (Backup)", "executionTime": 406, "siteType": "international"}, {"endpoint": "/api/ping-boce", "target": "instagram.com", "success": true, "totalNodes": 3, "successfulNodes": 3, "averagePing": 37, "minPing": 28, "maxPing": 42, "provider": "BOCE.COM", "dataSource": "BOCE.COM (模拟数据，因为网页抓取不稳定)", "executionTime": 366, "siteType": "international"}, {"endpoint": "/api/ping-aliyun-boce", "target": "instagram.com", "success": true, "totalNodes": 3, "successfulNodes": 3, "averagePing": 224, "minPing": 187, "maxPing": 265, "provider": "阿里云BOCE", "dataSource": "阿里云BOCE (模拟数据，因为网页抓取不稳定)", "executionTime": 254, "siteType": "international"}, {"endpoint": "/api/ping-uptimerobot", "target": "instagram.com", "success": true, "totalNodes": 3, "successfulNodes": 0, "averagePing": 0, "minPing": 0, "maxPing": 0, "provider": "UptimeRobot", "dataSource": "Unknown", "executionTime": 4, "siteType": "international"}, {"endpoint": "/api/ping-multi-reliable", "target": "instagram.com", "success": true, "totalNodes": 10, "successfulNodes": 10, "averagePing": 192, "minPing": 114, "maxPing": 339, "provider": "Multi-Reliable", "dataSource": "Multi-Reliable API", "executionTime": 11940, "siteType": "international"}, {"endpoint": "/api/ping-enhanced", "target": "instagram.com", "success": true, "totalNodes": 12, "successfulNodes": 12, "averagePing": 220, "minPing": 160, "maxPing": 310, "provider": "Enhanced <PERSON>", "dataSource": "Enhanced Ping API", "executionTime": 26280, "siteType": "international"}, {"endpoint": "/api/ping-global-test", "target": "instagram.com", "success": true, "totalNodes": 25, "successfulNodes": 23, "averagePing": 156, "minPing": 35, "maxPing": 341, "provider": "Global Test", "dataSource": "Global Test Network", "executionTime": 8, "siteType": "international"}]}