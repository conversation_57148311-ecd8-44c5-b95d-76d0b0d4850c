<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>延迟对比测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-group {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .chinese-sites {
            border-left: 4px solid #28a745;
            background: #f8fff9;
        }
        .foreign-sites {
            border-left: 4px solid #dc3545;
            background: #fff8f8;
        }
        .site-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: white;
            border-radius: 5px;
            border: 1px solid #eee;
        }
        .site-name {
            font-weight: bold;
            color: #333;
        }
        .latency {
            font-size: 18px;
            font-weight: bold;
        }
        .low-latency {
            color: #28a745;
        }
        .high-latency {
            color: #dc3545;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .loading {
            color: #007bff;
            font-style: italic;
        }
        .summary {
            background: #e9ecef;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .summary-item {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            font-size: 16px;
        }
        .summary-value {
            font-weight: bold;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 延迟对比测试</h1>
        <p>验证中国网站 vs 国外网站的延迟差异</p>
        
        <button onclick="runAllTests()" id="testBtn">开始对比测试</button>
        <button onclick="clearResults()">清除结果</button>

        <div class="summary" id="summary" style="display: none;">
            <h3>📊 测试总结</h3>
            <div class="summary-item">
                <span>中国网站平均延迟:</span>
                <span class="summary-value" id="chineseAvg">-</span>
            </div>
            <div class="summary-item">
                <span>国外网站平均延迟:</span>
                <span class="summary-value" id="foreignAvg">-</span>
            </div>
            <div class="summary-item">
                <span>延迟差异倍数:</span>
                <span class="summary-value" id="latencyRatio">-</span>
            </div>
        </div>

        <div class="test-group chinese-sites">
            <h3>🇨🇳 中国网站测试</h3>
            <div class="site-item">
                <span class="site-name">百度 (baidu.com)</span>
                <span class="latency" id="baidu-latency">-</span>
            </div>
            <div class="site-item">
                <span class="site-name">淘宝 (taobao.com)</span>
                <span class="latency" id="taobao-latency">-</span>
            </div>
            <div class="site-item">
                <span class="site-name">腾讯 (qq.com)</span>
                <span class="latency" id="qq-latency">-</span>
            </div>
            <div class="site-item">
                <span class="site-name">京东 (jd.com)</span>
                <span class="latency" id="jd-latency">-</span>
            </div>
        </div>

        <div class="test-group foreign-sites">
            <h3>🌍 国外网站测试</h3>
            <div class="site-item">
                <span class="site-name">YouTube (youtube.com)</span>
                <span class="latency" id="youtube-latency">-</span>
            </div>
            <div class="site-item">
                <span class="site-name">Google (google.com)</span>
                <span class="latency" id="google-latency">-</span>
            </div>
            <div class="site-item">
                <span class="site-name">GitHub (github.com)</span>
                <span class="latency" id="github-latency">-</span>
            </div>
            <div class="site-item">
                <span class="site-name">Facebook (facebook.com)</span>
                <span class="latency" id="facebook-latency">-</span>
            </div>
        </div>
    </div>

    <script>
        const testSites = {
            chinese: [
                { name: 'baidu', domain: 'baidu.com', displayName: '百度' },
                { name: 'taobao', domain: 'taobao.com', displayName: '淘宝' },
                { name: 'qq', domain: 'qq.com', displayName: '腾讯' },
                { name: 'jd', domain: 'jd.com', displayName: '京东' }
            ],
            foreign: [
                { name: 'youtube', domain: 'youtube.com', displayName: 'YouTube' },
                { name: 'google', domain: 'google.com', displayName: 'Google' },
                { name: 'github', domain: 'github.com', displayName: 'GitHub' },
                { name: 'facebook', domain: 'facebook.com', displayName: 'Facebook' }
            ]
        };

        async function testSite(domain) {
            try {
                const response = await fetch('/api/ping-comprehensive', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ target: domain })
                });

                const data = await response.json();
                
                if (data.success && data.results.length > 0) {
                    const validResults = data.results.filter(r => r.ping > 0);
                    const avgLatency = Math.round(validResults.reduce((sum, r) => sum + r.ping, 0) / validResults.length);
                    return avgLatency;
                }
                return null;
            } catch (error) {
                console.error(`测试 ${domain} 失败:`, error);
                return null;
            }
        }

        async function runAllTests() {
            const btn = document.getElementById('testBtn');
            btn.disabled = true;
            btn.textContent = '测试中...';

            // 清除之前的结果
            clearResults();

            const results = {
                chinese: [],
                foreign: []
            };

            // 测试中国网站
            for (const site of testSites.chinese) {
                const element = document.getElementById(`${site.name}-latency`);
                element.textContent = '测试中...';
                element.className = 'latency loading';

                const latency = await testSite(site.domain);
                if (latency !== null) {
                    element.textContent = `${latency}ms`;
                    element.className = 'latency low-latency';
                    results.chinese.push(latency);
                } else {
                    element.textContent = '失败';
                    element.className = 'latency';
                }

                // 等待一下再测试下一个
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            // 测试国外网站
            for (const site of testSites.foreign) {
                const element = document.getElementById(`${site.name}-latency`);
                element.textContent = '测试中...';
                element.className = 'latency loading';

                const latency = await testSite(site.domain);
                if (latency !== null) {
                    element.textContent = `${latency}ms`;
                    element.className = 'latency high-latency';
                    results.foreign.push(latency);
                } else {
                    element.textContent = '失败';
                    element.className = 'latency';
                }

                // 等待一下再测试下一个
                await new Promise(resolve => setTimeout(resolve, 500));
            }

            // 显示总结
            showSummary(results);

            btn.disabled = false;
            btn.textContent = '开始对比测试';
        }

        function showSummary(results) {
            const summary = document.getElementById('summary');
            
            if (results.chinese.length > 0 && results.foreign.length > 0) {
                const chineseAvg = Math.round(results.chinese.reduce((a, b) => a + b, 0) / results.chinese.length);
                const foreignAvg = Math.round(results.foreign.reduce((a, b) => a + b, 0) / results.foreign.length);
                const ratio = (foreignAvg / chineseAvg).toFixed(1);

                document.getElementById('chineseAvg').textContent = `${chineseAvg}ms`;
                document.getElementById('foreignAvg').textContent = `${foreignAvg}ms`;
                document.getElementById('latencyRatio').textContent = `${ratio}x`;

                summary.style.display = 'block';
            }
        }

        function clearResults() {
            // 清除所有延迟显示
            [...testSites.chinese, ...testSites.foreign].forEach(site => {
                const element = document.getElementById(`${site.name}-latency`);
                element.textContent = '-';
                element.className = 'latency';
            });

            // 隐藏总结
            document.getElementById('summary').style.display = 'none';
        }

        // 页面加载时自动运行测试
        window.onload = function() {
            setTimeout(() => {
                runAllTests();
            }, 1000);
        };
    </script>
</body>
</html>
