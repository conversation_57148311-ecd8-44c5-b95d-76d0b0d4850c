// 历史数据存储和分析系统

export interface HistoryRecord {
  id: string;
  target: string;
  timestamp: number;
  latency: number;
  jitter: number;
  packetLoss: number;
  bandwidth: number;
  downloadSpeed: number;
  uploadSpeed: number;
  mtu: number;
  status: 'success' | 'failed' | 'timeout';
  testMethod: string;
  reliability: number;
  location?: {
    city: string;
    province: string;
    region: string;
  };
}

export interface TrendData {
  timestamps: number[];
  latencies: number[];
  jitters: number[];
  packetLosses: number[];
  bandwidths: number[];
  averageLatency: number;
  averageJitter: number;
  averagePacketLoss: number;
  averageBandwidth: number;
  trend: 'improving' | 'degrading' | 'stable';
  reliability: number;
}

export interface PerformanceAlert {
  id: string;
  type: 'latency_spike' | 'packet_loss' | 'bandwidth_drop' | 'connection_failure';
  severity: 'low' | 'medium' | 'high' | 'critical';
  message: string;
  timestamp: number;
  target: string;
  value: number;
  threshold: number;
}

class HistoryStorage {
  private readonly STORAGE_KEY = 'ping_history';
  private readonly ALERTS_KEY = 'ping_alerts';
  private readonly MAX_RECORDS = 1000; // 最多保存1000条记录
  private readonly MAX_ALERTS = 100;   // 最多保存100条告警

  // 保存测试记录
  saveRecord(record: Omit<HistoryRecord, 'id'>): string {
    const id = this.generateId();
    const fullRecord: HistoryRecord = { ...record, id };
    
    const records = this.getAllRecords();
    records.push(fullRecord);
    
    // 保持记录数量在限制内
    if (records.length > this.MAX_RECORDS) {
      records.splice(0, records.length - this.MAX_RECORDS);
    }
    
    this.saveToStorage(this.STORAGE_KEY, records);
    
    // 检查是否需要生成告警
    this.checkForAlerts(fullRecord);
    
    return id;
  }

  // 获取所有记录
  getAllRecords(): HistoryRecord[] {
    return this.loadFromStorage(this.STORAGE_KEY, []);
  }

  // 根据目标获取记录
  getRecordsByTarget(target: string, limit?: number): HistoryRecord[] {
    const records = this.getAllRecords()
      .filter(record => record.target === target)
      .sort((a, b) => b.timestamp - a.timestamp);
    
    return limit ? records.slice(0, limit) : records;
  }

  // 获取时间范围内的记录
  getRecordsByTimeRange(startTime: number, endTime: number, target?: string): HistoryRecord[] {
    let records = this.getAllRecords()
      .filter(record => record.timestamp >= startTime && record.timestamp <= endTime);
    
    if (target) {
      records = records.filter(record => record.target === target);
    }
    
    return records.sort((a, b) => a.timestamp - b.timestamp);
  }

  // 获取趋势数据
  getTrendData(target: string, hours: number = 24): TrendData {
    const endTime = Date.now();
    const startTime = endTime - (hours * 60 * 60 * 1000);
    
    const records = this.getRecordsByTimeRange(startTime, endTime, target)
      .filter(record => record.status === 'success');

    if (records.length === 0) {
      return {
        timestamps: [],
        latencies: [],
        jitters: [],
        packetLosses: [],
        bandwidths: [],
        averageLatency: 0,
        averageJitter: 0,
        averagePacketLoss: 0,
        averageBandwidth: 0,
        trend: 'stable',
        reliability: 0
      };
    }

    const timestamps = records.map(r => r.timestamp);
    const latencies = records.map(r => r.latency);
    const jitters = records.map(r => r.jitter);
    const packetLosses = records.map(r => r.packetLoss);
    const bandwidths = records.map(r => r.bandwidth);

    // 计算平均值
    const averageLatency = latencies.reduce((a, b) => a + b, 0) / latencies.length;
    const averageJitter = jitters.reduce((a, b) => a + b, 0) / jitters.length;
    const averagePacketLoss = packetLosses.reduce((a, b) => a + b, 0) / packetLosses.length;
    const averageBandwidth = bandwidths.reduce((a, b) => a + b, 0) / bandwidths.length;

    // 计算趋势
    const trend = this.calculateTrend(latencies);
    
    // 计算可靠性
    const reliability = records.reduce((sum, r) => sum + r.reliability, 0) / records.length;

    return {
      timestamps,
      latencies,
      jitters,
      packetLosses,
      bandwidths,
      averageLatency: Math.round(averageLatency),
      averageJitter: Math.round(averageJitter),
      averagePacketLoss: Math.round(averagePacketLoss * 100) / 100,
      averageBandwidth: Math.round(averageBandwidth),
      trend,
      reliability: Math.round(reliability)
    };
  }

  // 计算趋势
  private calculateTrend(values: number[]): 'improving' | 'degrading' | 'stable' {
    if (values.length < 2) return 'stable';

    const mid = Math.floor(values.length / 2);
    const firstHalf = values.slice(0, mid);
    const secondHalf = values.slice(mid);

    const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;

    const change = ((secondAvg - firstAvg) / firstAvg) * 100;

    if (change > 10) return 'degrading';  // 延迟增加10%以上
    if (change < -10) return 'improving'; // 延迟减少10%以上
    return 'stable';
  }

  // 检查告警条件
  private checkForAlerts(record: HistoryRecord): void {
    const alerts: PerformanceAlert[] = [];

    // 延迟峰值告警
    if (record.latency > 500) {
      alerts.push({
        id: this.generateId(),
        type: 'latency_spike',
        severity: record.latency > 1000 ? 'critical' : 'high',
        message: `延迟峰值: ${record.latency}ms (目标: ${record.target})`,
        timestamp: record.timestamp,
        target: record.target,
        value: record.latency,
        threshold: 500
      });
    }

    // 丢包告警
    if (record.packetLoss > 5) {
      alerts.push({
        id: this.generateId(),
        type: 'packet_loss',
        severity: record.packetLoss > 20 ? 'critical' : 'medium',
        message: `丢包率过高: ${record.packetLoss}% (目标: ${record.target})`,
        timestamp: record.timestamp,
        target: record.target,
        value: record.packetLoss,
        threshold: 5
      });
    }

    // 带宽下降告警
    if (record.bandwidth > 0 && record.bandwidth < 100) {
      alerts.push({
        id: this.generateId(),
        type: 'bandwidth_drop',
        severity: record.bandwidth < 50 ? 'high' : 'medium',
        message: `带宽过低: ${record.bandwidth}Kbps (目标: ${record.target})`,
        timestamp: record.timestamp,
        target: record.target,
        value: record.bandwidth,
        threshold: 100
      });
    }

    // 连接失败告警
    if (record.status === 'failed' || record.status === 'timeout') {
      alerts.push({
        id: this.generateId(),
        type: 'connection_failure',
        severity: 'high',
        message: `连接失败: ${record.status} (目标: ${record.target})`,
        timestamp: record.timestamp,
        target: record.target,
        value: 0,
        threshold: 0
      });
    }

    // 保存告警
    if (alerts.length > 0) {
      this.saveAlerts(alerts);
    }
  }

  // 保存告警
  private saveAlerts(newAlerts: PerformanceAlert[]): void {
    const existingAlerts = this.getAllAlerts();
    const allAlerts = [...existingAlerts, ...newAlerts];
    
    // 保持告警数量在限制内
    if (allAlerts.length > this.MAX_ALERTS) {
      allAlerts.splice(0, allAlerts.length - this.MAX_ALERTS);
    }
    
    this.saveToStorage(this.ALERTS_KEY, allAlerts);
  }

  // 获取所有告警
  getAllAlerts(): PerformanceAlert[] {
    return this.loadFromStorage(this.ALERTS_KEY, []);
  }

  // 获取未读告警
  getUnreadAlerts(lastReadTime: number = 0): PerformanceAlert[] {
    return this.getAllAlerts()
      .filter(alert => alert.timestamp > lastReadTime)
      .sort((a, b) => b.timestamp - a.timestamp);
  }

  // 清除过期数据
  cleanupOldData(daysToKeep: number = 30): void {
    const cutoffTime = Date.now() - (daysToKeep * 24 * 60 * 60 * 1000);
    
    // 清理记录
    const records = this.getAllRecords()
      .filter(record => record.timestamp > cutoffTime);
    this.saveToStorage(this.STORAGE_KEY, records);
    
    // 清理告警
    const alerts = this.getAllAlerts()
      .filter(alert => alert.timestamp > cutoffTime);
    this.saveToStorage(this.ALERTS_KEY, alerts);
  }

  // 导出数据
  exportData(): { records: HistoryRecord[], alerts: PerformanceAlert[] } {
    return {
      records: this.getAllRecords(),
      alerts: this.getAllAlerts()
    };
  }

  // 导入数据
  importData(data: { records: HistoryRecord[], alerts: PerformanceAlert[] }): void {
    this.saveToStorage(this.STORAGE_KEY, data.records);
    this.saveToStorage(this.ALERTS_KEY, data.alerts);
  }

  // 工具方法
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private saveToStorage(key: string, data: any): void {
    try {
      localStorage.setItem(key, JSON.stringify(data));
    } catch (error) {
      // 保存到localStorage失败
    }
  }

  private loadFromStorage<T>(key: string, defaultValue: T): T {
    try {
      const data = localStorage.getItem(key);
      return data ? JSON.parse(data) : defaultValue;
    } catch (error) {
      // 从localStorage加载失败
      return defaultValue;
    }
  }
}

// 单例实例
export const historyStorage = new HistoryStorage();

export default HistoryStorage;
