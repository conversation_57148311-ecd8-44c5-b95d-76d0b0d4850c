<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单独API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .api-test {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            margin: 10px 0;
            padding: 15px;
            border-radius: 5px;
            background: #f8f9fa;
        }
        .success {
            border-left: 4px solid #28a745;
            background: #d4edda;
        }
        .error {
            border-left: 4px solid #dc3545;
            background: #f8d7da;
        }
        .loading {
            border-left: 4px solid #007bff;
            background: #d1ecf1;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .node-list {
            max-height: 200px;
            overflow-y: auto;
            margin: 10px 0;
            padding: 10px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .node-item {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #eee;
        }
        .node-item:last-child {
            border-bottom: none;
        }
        .latency-good {
            color: #28a745;
            font-weight: bold;
        }
        .latency-bad {
            color: #dc3545;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 单独API测试</h1>
        <p>直接测试各个真实API的响应情况</p>
        
        <div class="api-test">
            <h3>🇨🇳 中国网站测试 (baidu.com)</h3>
            <button onclick="testAPI('ITDOG', 'baidu.com')">测试 ITDOG.CN</button>
            <button onclick="testAPI('17CE', 'baidu.com')">测试 17CE.COM</button>
            <button onclick="testAPI('Chinaz', 'baidu.com')">测试 Chinaz.COM</button>
            <button onclick="testAPI('Globalping', 'baidu.com')">测试 Globalping.io</button>
            <button onclick="testAPI('KeyCDN', 'baidu.com')">测试 KeyCDN</button>
            <div id="baidu-results"></div>
        </div>

        <div class="api-test">
            <h3>🌍 国外网站测试 (youtube.com)</h3>
            <button onclick="testAPI('ITDOG', 'youtube.com')">测试 ITDOG.CN</button>
            <button onclick="testAPI('17CE', 'youtube.com')">测试 17CE.COM</button>
            <button onclick="testAPI('Chinaz', 'youtube.com')">测试 Chinaz.COM</button>
            <button onclick="testAPI('Globalping', 'youtube.com')">测试 Globalping.io</button>
            <button onclick="testAPI('KeyCDN', 'youtube.com')">测试 KeyCDN</button>
            <div id="youtube-results"></div>
        </div>

        <div class="api-test">
            <h3>🔄 批量测试</h3>
            <button onclick="testAllAPIs('baidu.com')">测试所有API - 百度</button>
            <button onclick="testAllAPIs('youtube.com')">测试所有API - YouTube</button>
            <button onclick="clearAllResults()">清除所有结果</button>
        </div>
    </div>

    <script>
        async function testAPI(apiName, target) {
            const resultsContainer = document.getElementById(`${target.split('.')[0]}-results`);
            
            // 创建或更新结果显示区域
            let resultDiv = document.getElementById(`${apiName}-${target.split('.')[0]}`);
            if (!resultDiv) {
                resultDiv = document.createElement('div');
                resultDiv.id = `${apiName}-${target.split('.')[0]}`;
                resultDiv.className = 'test-result loading';
                resultsContainer.appendChild(resultDiv);
            }
            
            resultDiv.className = 'test-result loading';
            resultDiv.innerHTML = `<h4>${apiName} - ${target}</h4><p>🔄 测试中...</p>`;

            try {
                const response = await fetch('/api/ping-comprehensive', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ target })
                });

                const data = await response.json();
                
                if (data.success && data.results.length > 0) {
                    // 查找指定API的结果
                    const apiResults = data.results.filter(r => 
                        r.apiSource === apiName || 
                        r.testMethod?.includes(apiName) ||
                        (apiName === 'ITDOG' && r.apiSource === 'ITDOG.CN') ||
                        (apiName === '17CE' && r.apiSource === '17CE') ||
                        (apiName === 'Chinaz' && r.apiSource === 'Chinaz') ||
                        (apiName === 'Globalping' && r.apiSource === 'Globalping.io') ||
                        (apiName === 'KeyCDN' && r.apiSource === 'KeyCDN')
                    );
                    
                    if (apiResults.length > 0) {
                        const avgLatency = Math.round(apiResults.reduce((sum, r) => sum + r.ping, 0) / apiResults.length);
                        const minLatency = Math.min(...apiResults.map(r => r.ping));
                        const maxLatency = Math.max(...apiResults.map(r => r.ping));
                        
                        resultDiv.className = 'test-result success';
                        resultDiv.innerHTML = `
                            <h4>✅ ${apiName} - ${target}</h4>
                            <p><strong>节点数:</strong> ${apiResults.length}</p>
                            <p><strong>平均延迟:</strong> ${avgLatency}ms</p>
                            <p><strong>延迟范围:</strong> ${minLatency}ms - ${maxLatency}ms</p>
                            <div class="node-list">
                                ${apiResults.map(r => `
                                    <div class="node-item">
                                        <span>${r.node || r.location?.city || '未知节点'}</span>
                                        <span class="${r.ping < 100 ? 'latency-good' : 'latency-bad'}">${r.ping}ms</span>
                                    </div>
                                `).join('')}
                            </div>
                        `;
                    } else {
                        resultDiv.className = 'test-result error';
                        resultDiv.innerHTML = `
                            <h4>❌ ${apiName} - ${target}</h4>
                            <p>该API没有返回数据</p>
                            <p>可能原因：API不支持该网站或服务暂时不可用</p>
                        `;
                    }
                } else {
                    resultDiv.className = 'test-result error';
                    resultDiv.innerHTML = `
                        <h4>❌ ${apiName} - ${target}</h4>
                        <p>测试失败: ${data.error || '未知错误'}</p>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'test-result error';
                resultDiv.innerHTML = `
                    <h4>❌ ${apiName} - ${target}</h4>
                    <p>网络错误: ${error.message}</p>
                `;
            }
        }

        async function testAllAPIs(target) {
            const apis = ['ITDOG', '17CE', 'Chinaz', 'Globalping', 'KeyCDN'];
            
            for (const api of apis) {
                await testAPI(api, target);
                // 等待一下再测试下一个API
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }

        function clearAllResults() {
            const resultContainers = document.querySelectorAll('[id$="-results"]');
            resultContainers.forEach(container => {
                container.innerHTML = '';
            });
        }

        // 页面加载时自动测试一下
        window.onload = function() {
            setTimeout(() => {
                testAPI('ITDOG', 'baidu.com');
            }, 1000);
        };
    </script>
</body>
</html>
