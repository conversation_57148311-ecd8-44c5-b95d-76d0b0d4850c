'use client';

import React, { useState, useEffect } from 'react';

interface VisitCounterProps {
  isDarkMode: boolean;
}

const VisitCounter: React.FC<VisitCounterProps> = ({ isDarkMode }) => {
  const [visitCount, setVisitCount] = useState<number>(0);
  const [isAnimating, setIsAnimating] = useState<boolean>(false);


  useEffect(() => {
    // 🌐 启用Redis访问统计
    const recordVisit = async () => {
      try {
        // 使用Redis API进行访问统计
        const response = await fetch('/api/visit-stats', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ action: 'increment' }),
        });

        if (response.ok) {
          const data = await response.json();
          // 处理API返回的数据格式
          if (data.success || data.visits) {
            const count = parseInt(data.count || data.visits || '0', 10);
            if (count > 0) {
              setVisitCount(count);
              return;
            }
          }
        }

        // 如果API失败，回退到localStorage
        const storedCount = localStorage.getItem('ping-tool-visit-count');
        const currentCount = storedCount ? parseInt(storedCount, 10) : 0;
        const newCount = currentCount + 1;
        setVisitCount(newCount);
        localStorage.setItem('ping-tool-visit-count', newCount.toString());
      } catch (error) {
        console.warn('访问统计失败，使用本地存储:', error);
        // 回退到localStorage
        const storedCount = localStorage.getItem('ping-tool-visit-count');
        const currentCount = storedCount ? parseInt(storedCount, 10) : 1267;
        const newCount = currentCount + 1;
        setVisitCount(newCount);
        localStorage.setItem('ping-tool-visit-count', newCount.toString());
      }
    };

    recordVisit();

    // 触发动画
    setIsAnimating(true);
    const timer = setTimeout(() => setIsAnimating(false), 1500);

    return () => clearTimeout(timer);
  }, []);

  // 格式化数字显示（添加千位分隔符）
  const formatNumber = (num: number): string => {
    return num.toLocaleString();
  };



  return (
    <div
      className={`relative inline-flex items-center px-4 py-2 rounded-full shadow-lg transition-all duration-300 hover:shadow-xl select-none ${
        isDarkMode
          ? 'bg-gradient-to-r from-gray-800 to-gray-700 border border-gray-600'
          : 'bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200'
      } ${isAnimating ? 'scale-110' : 'scale-100'}`}
      title="访问次数统计"
    >
      {/* 图标 */}
      <svg className={`w-4 h-4 mr-2 ${
        isDarkMode ? 'text-blue-400' : 'text-blue-600'
      }`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
              d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2}
              d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
      </svg>

      {/* 访问次数文本 */}
      <span className={`text-sm font-medium ${
        isDarkMode ? 'text-gray-300' : 'text-gray-700'
      }`}>
        访问次数：
      </span>
      <span className={`ml-1 text-lg font-bold ${
        isDarkMode ? 'text-blue-400' : 'text-blue-600'
      } ${isAnimating ? 'animate-bounce' : ''} transition-all duration-300`}>
        {formatNumber(visitCount)}
      </span>

      {/* 动态状态指示器 */}
      <div className={`ml-3 w-2 h-2 rounded-full ${
        isDarkMode ? 'bg-green-400' : 'bg-green-500'
      } ${isAnimating ? 'animate-ping' : ''} shadow-lg`}></div>

      {/* 增强的光效 */}
      {isAnimating && (
        <div className={`absolute inset-0 rounded-full ${
          isDarkMode ? 'bg-blue-400/20' : 'bg-blue-500/20'
        } animate-pulse`}></div>
      )}

      {/* 边框光效 */}
      <div className={`absolute inset-0 rounded-full ${
        isDarkMode ? 'ring-2 ring-blue-400/30' : 'ring-2 ring-blue-500/30'
      } ${isAnimating ? 'animate-pulse' : ''}`}></div>
    </div>
  );
};

export default VisitCounter;
