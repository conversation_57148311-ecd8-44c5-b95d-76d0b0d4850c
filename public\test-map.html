<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地图测试 - 中国各省市ping延迟</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-info {
            background: #2d2d2d;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .node-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 20px;
        }
        .node-item {
            background: #3d3d3d;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
        }
        .ping-good { color: #16a34a; }
        .ping-ok { color: #22c55e; }
        .ping-fair { color: #84cc16; }
        .ping-slow { color: #eab308; }
        .ping-bad { color: #ea580c; }
        .test-button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .test-button:hover {
            background: #2563eb;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #374151;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🗺️ 中国各省市ping延迟测试</h1>
        
        <div class="test-info">
            <h2>测试说明</h2>
            <p>这个页面用于测试Enhanced Ping API返回的41个中国省市节点数据。</p>
            <p>点击下面的按钮测试不同网站的ping延迟分布。</p>
            
            <button class="test-button" onclick="testWebsite('https://www.baidu.com/')">测试百度</button>
            <button class="test-button" onclick="testWebsite('https://www.qq.com/')">测试QQ</button>
            <button class="test-button" onclick="testWebsite('https://www.taobao.com/')">测试淘宝</button>
            <button class="test-button" onclick="testWebsite('https://www.google.com/')">测试Google</button>
            
            <div id="status" class="status">准备测试...</div>
        </div>

        <div id="results"></div>
    </div>

    <script>
        async function testWebsite(url) {
            const statusEl = document.getElementById('status');
            const resultsEl = document.getElementById('results');
            
            statusEl.innerHTML = `🚀 正在测试 ${url}...`;
            resultsEl.innerHTML = '';
            
            try {
                const response = await fetch('/api/ping-multiplatform', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ target: url })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    displayResults(url, data.results, data.metadata);
                    statusEl.innerHTML = `✅ 测试完成！共获得 ${data.results.length} 个节点数据`;
                } else {
                    statusEl.innerHTML = `❌ 测试失败: ${data.error}`;
                }
            } catch (error) {
                statusEl.innerHTML = `❌ 网络错误: ${error.message}`;
            }
        }
        
        function displayResults(url, results, metadata) {
            const resultsEl = document.getElementById('results');
            
            // 分离中国节点和国际节点
            const chineseNodes = results.filter(r => r.location.country === 'CN');
            const globalNodes = results.filter(r => r.location.country !== 'CN');
            
            // 按延迟排序
            chineseNodes.sort((a, b) => a.ping - b.ping);
            globalNodes.sort((a, b) => a.ping - b.ping);
            
            const html = `
                <div class="test-info">
                    <h2>📊 ${url} 测试结果</h2>
                    <p><strong>总节点数:</strong> ${results.length}</p>
                    <p><strong>中国节点:</strong> ${chineseNodes.length} 个</p>
                    <p><strong>国际节点:</strong> ${globalNodes.length} 个</p>
                    <p><strong>测试用时:</strong> ${metadata.executionTime}ms</p>
                    <p><strong>使用服务商:</strong> ${metadata.providersUsed.join(', ')}</p>
                </div>
                
                <div class="test-info">
                    <h3>🇨🇳 中国节点 (${chineseNodes.length}个)</h3>
                    <div class="node-list">
                        ${chineseNodes.map(node => `
                            <div class="node-item">
                                <div class="${getPingClass(node.ping)}">${node.node}</div>
                                <div class="${getPingClass(node.ping)}">${node.ping}ms</div>
                                <div style="color: #9ca3af; font-size: 10px;">${node.testMethod}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
                
                ${globalNodes.length > 0 ? `
                <div class="test-info">
                    <h3>🌍 国际节点 (${globalNodes.length}个)</h3>
                    <div class="node-list">
                        ${globalNodes.map(node => `
                            <div class="node-item">
                                <div class="${getPingClass(node.ping)}">${node.node}</div>
                                <div class="${getPingClass(node.ping)}">${node.ping}ms</div>
                                <div style="color: #9ca3af; font-size: 10px;">${node.testMethod}</div>
                            </div>
                        `).join('')}
                    </div>
                </div>
                ` : ''}
            `;
            
            resultsEl.innerHTML = html;
        }
        
        function getPingClass(ping) {
            if (ping <= 50) return 'ping-good';
            if (ping <= 100) return 'ping-ok';
            if (ping <= 200) return 'ping-fair';
            if (ping <= 250) return 'ping-slow';
            return 'ping-bad';
        }
    </script>
</body>
</html>
